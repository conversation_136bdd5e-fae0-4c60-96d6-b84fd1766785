package main

import (
	"agilize/src/drivers/datastores"
	"agilize/src/drivers/servers/httpserver"
	"agilize/src/http/routes"
	"agilize/src/shared/configurator"
	"agilize/src/shared/logger"
	"flag"
	"os"
	"os/signal"
	"syscall"

	"github.com/asaskevich/govalidator"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
)

func init() {
	var configFile string
	flag.StringVar(&configFile, "config", "", "Configuration file")
	flag.Parse()
	config := configurator.Load(configFile)
	logger.Init(logger.Config{
		Level:  config.Log.Level,
		Format: config.Log.Format,
		Output: config.Log.Output,
	})
}

func main() {
	ds := datastores.New()
	defer datastores.CloseConnections()

	if configurator.GetAppConfig().App.Environment != "local" {
		ddService := os.Getenv("DD_SERVICE")

		tracer.Start(
			tracer.WithEnv(os.Getenv("DD_ENV")),
			tracer.WithService(ddService),
			tracer.WithServiceVersion("1.0.0"),
		)
		defer tracer.Stop()
	}

	govalidator.SetFieldsRequiredByDefault(true)

	httpServer := httpserver.New()
	httpServer.AddHealthCheck("/health")
	httpServer.UseDefaultMiddlewares()
	httpServer.ServeStaticFiles("/static")
	router := httpServer.Router()
	routes.NewRouteManager(router, ds).Setup()

	go func() {
		_ = httpServer.Start()
	}()
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt, syscall.SIGTERM, syscall.SIGQUIT)
	<-quit
}
