package command

import (
	workerRepository "agilize/src/application/processos/adapters/repositories"
	"agilize/src/application/processos/src/domain/models"
	"agilize/src/application/processos/src/domain/service"
	"agilize/src/application/processos/src/ports/input"
	"agilize/src/application/usecase/declaracao"
	"agilize/src/application/usecase/declaracao/handlers"
	"agilize/src/drivers/bus"
	"agilize/src/drivers/datastores"
	"agilize/src/shared/camunda"
	"agilize/src/shared/logger"
	"agilize/src/shared/requestcadence"
	"context"
	"fmt"
	"time"
)

type ProcessarDeclaracoesMitConfig struct {
	Threads  int
	MaxTasks int
}

func ProcessarDeclaracoesMitCommand(config ProcessarDeclaracoesMitConfig, logger logger.Logger) {
	log = logger
	ds := datastores.New()
	camundabus = bus.NewCamundaBus(ds)
	requestcadence.NewFibbonacciRequestCadence(time.Second * 10)

	startWorkerMit(ds, config)
}

func startWorkerMit(ds datastores.DataStore, config ProcessarDeclaracoesMitConfig) {
	ctx := context.Background()
	workerRepo, worker := setupWorkerMitInformations(ds)

	go signalHandler(workerRepo, ctx, worker.ID)

	payload := setupExternalTaskDeclaracaoMitPayload(worker, config)
	err := camundabus.ConsumeExternalTask(payload, func(task camunda.FetchAndLockExternalTask) {
		if !commandConfig.isLocal {
			log = logger.InitDbLogger(task.BusinessKey)
		}
		log.Info("Pegando task de business key " + task.BusinessKey + ", topic name: " + task.TopicName)
		crawlersContainer,
			declaracaoRepoContainer,
			artefatoRepositoryContainer,
			processoContainer,
			notificacaoContainer,
			empresaContainer := setupContainers(ds)

		artefatoService,
			notificacaoService,
			declaracaoService := setupServices(artefatoRepositoryContainer, ds, notificacaoContainer, declaracaoRepoContainer, crawlersContainer)

		executarDeclaracao := declaracao.NewExecutarDeclaracaoUseCase(
			ds,
			crawlersContainer.GetDeclaracoesMunicipais(),
			artefatoService,
			notificacaoService,
			notificacaoContainer.CompanyRepository(),
			notificacaoContainer.NotificacaoTemplateRepository(),
			declaracaoService,
			service.NewProcessoService(processoContainer),
			processoContainer.ProcessInstanceRepository(),
			log,
			empresaContainer.EmpresaRepository(),
			declaracaoRepoContainer.AccountingAcessorRepository(),
		)
		camundaVariables, executionErr := executarDeclaracao.Execute(context.Background(), task)
		if executionErr != nil {
			fmt.Printf("Entrou no erro de execução %s", executionErr.Error())

			handleError(task, executionErr, camundaVariables, workerRepo, ctx, worker)
			return
		}

		isCompleted, errComplete := camundabus.CompleteExternalTask(task.ID, camunda.ExternalTasksCompletePayload{
			WorkerID:  task.WorkerID,
			Variables: camundaVariables,
		})

		if !isCompleted && errComplete != nil {
			log.Error(errComplete.Error() + " - Não foi possível completar atividade no camunda - **********")
		}
	})

	if err != nil {
		_ = workerRepo.ReleaseWorker(ctx, worker.ID)
		log.Fatal(err.Error() + "- **********")
	}
}

func setupExternalTaskDeclaracaoMitPayload(worker models.ProcessWorker, config ProcessarDeclaracoesMitConfig) camunda.FetchAndLockPayload {
	tenant := []string{"accounting"}
	processDefinitionKey := "declaracoes_mit"
	payload := camunda.FetchAndLockPayload{
		WorkerID:             worker.Name,
		MaxTasks:             config.MaxTasks,
		AsyncResponseTimeout: 18000,
		UsePriority:          true,
		Topics: []camunda.Topic{
			{
				Name:                 handlers.CriarDeclaracaoTopic,
				LockDuration:         3000,
				TenantIdIn:           tenant,
				ProcessDefinitionKey: processDefinitionKey,
				Variables:            nil,
			},
			{
				Name:                 handlers.CriarDeclaracaoLegacyTopic,
				LockDuration:         3000,
				TenantIdIn:           tenant,
				ProcessDefinitionKey: processDefinitionKey,
				Variables:            nil,
			},
		},
	}
	return payload
}

func setupWorkerMitInformations(ds datastores.DataStore) (input.ProcessWorkersRepository, models.ProcessWorker) {
	workerRepo := workerRepository.NewProcessWorkersRepository(ds.Postgres())
	worker, _ := workerRepo.FindAndLockFreeWorker(context.Background(), workerRepository.DECLARACAO_MIT_PROCESS_ID)
	if worker.ID == "" {
		log.Fatal("Worker não encontrado, verifique se seu banco tem workers na tabela process_workers...")
	}
	log.Info("Worker: " + worker.Name)
	return workerRepo, worker
}
