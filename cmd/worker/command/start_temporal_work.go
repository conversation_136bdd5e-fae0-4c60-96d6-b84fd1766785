package command

import (
	"agilize/src/application/empresa/adapters/repositories"
	"agilize/src/application/empresa/src/domain/services"
	repositories2 "agilize/src/application/notificacao/adapters/repositories"
	services2 "agilize/src/application/notificacao/src/domain/services"
	repositories3 "agilize/src/application/processos/adapters/repositories"
	"agilize/src/application/usecase/certificado/renovacao_certificado_workflow/workflows"
	"agilize/src/drivers/datastores"
	"agilize/src/drivers/templates"
	"agilize/src/drivers/temporalio"
	"agilize/src/shared/logger"
)

func StartTemporalWorkerCommand(log logger.Logger) {
	defaultWorkerContainer := temporalio.NewWorkerContainer(log)
	registrarRenovacaoCertificado(log, datastores.New(), defaultWorkerContainer)

	if err := defaultWorkerContainer.Start(); err != nil {
		log.Fatal(err.Error() + " - 1724938901")
	}
}

func registrarRenovacaoCertificado(log logger.Logger, ds datastores.DataStore, c *temporalio.DefaultWorkerContainer) {
	empresaContainer := repositories.NewEmpresaRepositoryContainer(ds)
	processoContainer := repositories3.NewProcessoRepositoryContainer(ds)
	renovacaoCertificadoService := services.NewRenovacaoCertificadoService(empresaContainer.CertificadoRepository(), empresaContainer.EmpresaRepository())
	notificacaoContainer := repositories2.NewNotificacaoRepositoryContainer(ds)
	notificacaoService := services2.NewNotificacaoService(
		notificacaoContainer.NotificacaoRepository(),
		notificacaoContainer.NotificacaoAcessorRepository(),
		notificacaoContainer.AccountingAcessorRepository(),
		notificacaoContainer.CompanyRepository(),
		notificacaoContainer.NotificacaoTemplateRepository(),
		templates.NewTemplate(),
	)
	certificadoNotificacaoService := services2.NewCertificadoNotificacaoService(
		notificacaoService,
		notificacaoContainer.NotificacaoTemplateRepository(),
		empresaContainer.AcessorRepository(),
	)

	rcWorkflowContainer := workflows.New(
		ds,
		empresaContainer,
		renovacaoCertificadoService,
		notificacaoContainer,
		notificacaoService,
		processoContainer,
		certificadoNotificacaoService,
	)
	renovacaoCertificadoWorker := workflows.NewWorker(c.GetWorker())
	// err := c.GetWorker().AddSearchAttributes("cnpj")
	// if err != nil {
	// 	msg := fmt.Sprintf("Erro ao adicionar atributo cnpj - **********. Motivo: %s", err.Error())
	// 	log.Fatal(msg)
	// }
	// err = c.GetWorker().AddSearchAttributes("processInstanceId")
	// if err != nil {
	// 	msg := fmt.Sprintf("Erro ao adicionar atributo processInstanceId - **********. Motivo: %s", err.Error())
	// 	log.Fatal(msg)
	// }
	renovacaoCertificadoWorker.RegistrarWorkflow(rcWorkflowContainer)
}
