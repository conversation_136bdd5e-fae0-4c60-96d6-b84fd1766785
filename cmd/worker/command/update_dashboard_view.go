package command

import (
	"agilize/src/application/processos/adapters/repositories"
	"agilize/src/application/usecase/processos"
	"agilize/src/drivers/datastores"
	"agilize/src/shared/configurator"
	"agilize/src/shared/logger"
	"context"
	"errors"
)

func init() {
	commandSetup = CommandSetup{
		processoContainer: repositories.NewProcessoRepositoryContainer(datastores.New()),
	}
	log = logger.Init(logger.Config{
		Level:  configurator.GetAppConfig().Log.Level,
		Format: configurator.GetAppConfig().Log.Format,
		Output: configurator.GetAppConfig().Log.Output,
	})
}

func NewUpdateDashboardViewCommand() {
	useCase := processos.NewUpdateDashboardViewUseCase(commandSetup.processoContainer.DashboardRepository())
	err := useCase.Execute(context.Background())
	if err != nil {
		log.Panic(errors.New("1719491082 - erro ao atualizar a view, motivo: " + err.Error()))
	}
}
