package converters

import (
	"agilize/src/application/notificacao/adapters/dto"
	"agilize/src/application/notificacao/src/domain/models"
)

type NotificacaoConverter struct {
}

func NewNotificacaoConverter() NotificacaoConverter {
	return NotificacaoConverter{}
}

func (nc NotificacaoConverter) FromNotificacaoTableListToModelList(inputs []dto.NotificacaoTable) []models.Notificacao {
	var notificacoes []models.Notificacao
	for _, input := range inputs {
		newNotificacao := nc.FromNotificacaoTableToModel(input)
		notificacoes = append(notificacoes, newNotificacao)
	}
	return notificacoes
}

func (nc NotificacaoConverter) FromNotificacaoTableToModel(input dto.NotificacaoTable) models.Notificacao {
	channel := models.NotificationChannel(input.NotificationChannel)
	email := ""
	contactName := ""

	if input.ContactInfo.Valid {
		email = input.ContactInfo.String
	}

	if input.ContactName.Valid {
		contactName = input.ContactName.String
	}

	return models.Notificacao{
		ID:                  input.Id,
		ScheduleDate:        input.ScheduleDate,
		NotificationChannel: channel,
		Subject:             input.Subject,
		Body:                input.Body,
		EmailInfo: models.EmailInfo{
			Email:       email,
			ContactName: contactName,
		},
	}
}
