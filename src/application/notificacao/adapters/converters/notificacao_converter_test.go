package converters

import (
	"agilize/src/application/notificacao/adapters/dto"
	"agilize/src/application/notificacao/src/domain/models"
	"agilize/src/shared/testify"
	"agilize/src/shared/testify/assert"
	"database/sql"
	"testing"
	"time"
)

func TestNotificacaoConverter(ts *testing.T) {
	testify.It(ts, "Converte com dados basicos", func(t *testing.T) {
		input := dto.NotificacaoTable{
			Id:                  "24cb17fb-f798-4387-9991-7466365d3aa0",
			Body:                "Teste de corpo",
			Subject:             "Teste assunto",
			ScheduleDate:        time.Time{},
			NotificationChannel: "email",
			ContactInfo:         sql.NullString{},
			ContactName:         sql.NullString{},
		}

		out := NewNotificacaoConverter().FromNotificacaoTableToModel(input)
		assert.Equal(t, out.ID, "24cb17fb-f798-4387-9991-7466365d3aa0")
		assert.Equal(t, out.Body, "Teste de corpo")
		assert.Equal(t, out.Subject, "Teste assunto")
		assert.Equal(t, out.ScheduleDate, time.Time{})
		assert.Equal(t, out.NotificationChannel, models.NotificationChannel("email"))
		assert.Equal(t, out.EmailInfo.Email, "")
		assert.Equal(t, out.EmailInfo.ContactName, "")
	})

	testify.It(ts, "Converte com dados totalmente preenchidos", func(t *testing.T) {
		input := dto.NotificacaoTable{
			Id:                  "24cb17fb-f798-4387-9991-7466365d3aa0",
			Body:                "Teste de corpo",
			Subject:             "Teste assunto",
			ScheduleDate:        time.Time{},
			NotificationChannel: "email",
			ContactInfo: sql.NullString{
				String: "<EMAIL>",
				Valid:  true,
			},
			ContactName: sql.NullString{
				String: "Teste Email",
				Valid:  true,
			},
		}

		out := NewNotificacaoConverter().FromNotificacaoTableToModel(input)
		assert.Equal(t, out.ID, "24cb17fb-f798-4387-9991-7466365d3aa0")
		assert.Equal(t, out.Body, "Teste de corpo")
		assert.Equal(t, out.Subject, "Teste assunto")
		assert.Equal(t, out.ScheduleDate, time.Time{})
		assert.Equal(t, out.NotificationChannel, models.NotificationChannel("email"))
		assert.Equal(t, out.EmailInfo.Email, "<EMAIL>")
		assert.Equal(t, out.EmailInfo.ContactName, "Teste Email")
	})

	testify.It(ts, "Converte com dados de telefone", func(t *testing.T) {
		input := dto.NotificacaoTable{
			Id:                  "24cb17fb-f798-4387-9991-7466365d3aa0",
			Body:                "Teste de corpo",
			Subject:             "Teste assunto",
			ScheduleDate:        time.Time{},
			NotificationChannel: "sms",
			ContactInfo: sql.NullString{
				String: "719851685681",
				Valid:  true,
			},
			ContactName: sql.NullString{
				String: "Teste telefone",
				Valid:  true,
			},
		}

		out := NewNotificacaoConverter().FromNotificacaoTableToModel(input)
		assert.Equal(t, out.ID, "24cb17fb-f798-4387-9991-7466365d3aa0")
		assert.Equal(t, out.Body, "Teste de corpo")
		assert.Equal(t, out.Subject, "Teste assunto")
		assert.Equal(t, out.ScheduleDate, time.Time{})
		assert.Equal(t, out.NotificationChannel, models.NotificationChannel("sms"))
		assert.Equal(t, out.EmailInfo.Email, "719851685681")
		assert.Equal(t, out.EmailInfo.ContactName, "Teste telefone")
	})
}
