package converters

import (
	"agilize/src/drivers/datastores/notificacao_acessor"
	"agilize/src/shared/testify"
	"agilize/src/shared/testify/assert"
	"testing"
)

func TestFromNotificacaoAcessorResponseModel(ts *testing.T) {
	testify.It(ts, "Convert Notificacao Data response to model", func(t *testing.T) {
		input := notificacao_acessor.NotificacaoMsDataResponseDto{
			ID:                   "ec37ae8e-ea4b-47e7-a107-4cbdd49573b2",
			ScheduleDate:         "2024-01-01 00:00:00",
			NotificationType:     "email",
			DeliveryStatus:       1,
			NotificationTemplate: "",
			AppID:                "41bab96e-7eab-4418-84cc-2fc755d5dc4e",
			CreatedAt:            "2024-01-01 00:00:00",
			UpdatedAt:            "2024-01-01 00:00:00",
		}

		out := NewNotificacaoAcessorConverter().FromNotificacaoAcessorResponseToModel(input)
		assert.Equal(t, out.ID, "41bab96e-7eab-4418-84cc-2fc755d5dc4e")
		assert.Equal(t, out.CorrelationId, "ec37ae8e-ea4b-47e7-a107-4cbdd49573b2")
	})
}
