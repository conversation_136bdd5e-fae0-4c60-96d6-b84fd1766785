package converters

import (
	"agilize/src/application/notificacao/src/domain/models"
	"agilize/src/drivers/datastores/notificacao_acessor"
)

type NotificacaoAcessorConverter struct {
}

func NewNotificacaoAcessorConverter() NotificacaoAcessorConverter {
	return NotificacaoAcessorConverter{}
}

func (nc NotificacaoAcessorConverter) FromNotificacaoAcessorResponseToModelList(inputs []notificacao_acessor.NotificacaoMsDataResponseDto) []models.Notificacao {
	var notificacoes []models.Notificacao
	for _, input := range inputs {
		newNotificacao := nc.FromNotificacaoAcessorResponseToModel(input)
		notificacoes = append(notificacoes, newNotificacao)
	}
	return notificacoes
}

func (nc NotificacaoAcessorConverter) FromNotificacaoAcessorResponseToModel(input notificacao_acessor.NotificacaoMsDataResponseDto) models.Notificacao {
	return models.Notificacao{ID: input.AppID, CorrelationId: input.ID}
}
