package converters

import (
	"agilize/src/application/notificacao/adapters/dto"
	"agilize/src/shared/testify"
	"agilize/src/shared/testify/assert"
	"testing"
)

func TestFromTableModel(ts *testing.T) {
	testify.It(ts, "Convert Notificacao Data response to model", func(t *testing.T) {
		textoSms := "Corpo do Sms"
		input := dto.NotificacaoTemplateTable{
			Id:       "6c70e2d7-adfe-4aeb-a88b-27d75060327d",
			Slugname: "teste",
			Nome:     "Template teste",
			Body:     "Corpo do template",
			Subject:  "Subject teste",
			SmsBody:  &textoSms,
		}

		out := NewNotificacaoTemplateConverter().FromTableToModel(input)
		assert.Equal(t, out.ID, "6c70e2d7-adfe-4aeb-a88b-27d75060327d")
		assert.Equal(t, out.Slugname, "teste")
		assert.Equal(t, out.Nome, "Template teste")
		assert.Equal(t, out.Body, "Corpo do template")
		assert.Equal(t, out.Subject, "Subject teste")
		assert.Equal(t, *out.SmsBody, "Corpo do Sms")
	})
}
