package converters

import (
	"agilize/src/application/notificacao/adapters/dto"
	"agilize/src/shared/testify"
	"agilize/src/shared/testify/assert"
	"testing"
)

func TestFromTableToModel(ts *testing.T) {
	testify.It(ts, "Convert company table to model", func(t *testing.T) {
		input := dto.CompanyTable{
			Id:   "teste",
			Cnpj: "*********",
			Nome: "Empresa teste",
		}

		out := NewCompanyConverter().FromTableToModel(input)
		assert.Equal(t, out.ID, "teste")
		assert.Equal(t, out.Cnpj, "*********")
		assert.Equal(t, out.Nome, "Empresa teste")
	})
}
