package converters

import (
	"agilize/src/application/notificacao/src/domain/models"
	"agilize/src/drivers/datastores/accounting_acessor"
)

type AccountingAcessorConverter struct {
}

func NewAccountingAcessorConverter() AccountingAcessorConverter {
	return AccountingAcessorConverter{}
}

func (aac AccountingAcessorConverter) FromEmailAccountingAcessorToModelList(inputs []accounting_acessor.EmailAcessor) []models.Email {
	var emails []models.Email
	for _, input := range inputs {
		newEmail := aac.FromEmailAccountingAcessorToModel(input)
		emails = append(emails, newEmail)
	}
	return emails
}

func (aac AccountingAcessorConverter) FromEmailAccountingAcessorToModel(input accounting_acessor.EmailAcessor) models.Email {
	return models.Email{Email: input.Email, ContactName: input.ContactName}
}

func (aac AccountingAcessorConverter) FromTelefoneAccountingAcessorToModelList(inputs []accounting_acessor.TelefoneAcessor) []models.Telefone {
	var telefones []models.Telefone
	for _, input := range inputs {
		newTelefone := aac.FromTelefoneAccountingAcessorToModel(input)
		telefones = append(telefones, newTelefone)
	}
	return telefones
}

func (aac AccountingAcessorConverter) FromTelefoneAccountingAcessorToModel(input accounting_acessor.TelefoneAcessor) models.Telefone {
	return models.Telefone{Ddd: input.Ddd, Numero: input.Numero}
}

func (aac AccountingAcessorConverter) FromCnaeAccountingAcessorToModelList(inputs []accounting_acessor.Cnaes) []models.Cnae {
	var cnaes []models.Cnae
	for _, input := range inputs {
		newCnae := aac.FromCnaeAccountingAcessorToModel(input)
		cnaes = append(cnaes, newCnae)
	}
	return cnaes
}

func (aac AccountingAcessorConverter) FromCnaeAccountingAcessorToModel(input accounting_acessor.Cnaes) models.Cnae {
	return models.Cnae{CnaeCode: input.Code, CnaeDescription: input.Description}
}
