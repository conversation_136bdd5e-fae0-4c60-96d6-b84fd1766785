package converters

import (
	"agilize/src/application/notificacao/adapters/dto"
	"agilize/src/application/notificacao/src/domain/models"
)

type CompanyConverter struct {
}

func NewCompanyConverter() CompanyConverter {
	return CompanyConverter{}
}

func (c CompanyConverter) FromTableToModel(input dto.CompanyTable) models.Company {
	output := models.Company{}
	output.ID = input.Id
	output.Cnpj = input.Cnpj
	output.Nome = input.Nome
	return output
}
