package converters

import (
	"agilize/src/drivers/datastores/accounting_acessor"
	"agilize/src/shared/testify"
	"agilize/src/shared/testify/assert"
	"testing"
)

func TestFromEmailAccountingToModel(ts *testing.T) {
	testify.It(ts, "Convert accounting email to model", func(t *testing.T) {
		input := accounting_acessor.EmailAcessor{
			Email:       "<EMAIL>",
			ContactName: "Contato de teste",
		}

		out := NewAccountingAcessorConverter().FromEmailAccountingAcessorToModel(input)
		assert.Equal(t, out.Email, "<EMAIL>")
		assert.Equal(t, out.ContactName, "Contato de teste")
	})
}

func TestFromPhoneAccountingToModel(ts *testing.T) {
	testify.It(ts, "Convert accounting telefone to model", func(t *testing.T) {
		input := accounting_acessor.TelefoneAcessor{
			Ddd:    "71",
			Numero: "***********",
		}

		out := NewAccountingAcessorConverter().FromTelefoneAccountingAcessorToModel(input)
		assert.Equal(t, out.Ddd, "71")
		assert.Equal(t, out.Numero, "***********")
	})
}
