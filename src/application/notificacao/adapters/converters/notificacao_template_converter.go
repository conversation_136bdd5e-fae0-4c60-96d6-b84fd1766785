package converters

import (
	"agilize/src/application/notificacao/adapters/dto"
	"agilize/src/application/notificacao/src/domain/models"
)

type NotificacaoTemplateConverter struct {
}

func NewNotificacaoTemplateConverter() NotificacaoTemplateConverter {
	return NotificacaoTemplateConverter{}
}

func (ntc NotificacaoTemplateConverter) FromTableToModel(input dto.NotificacaoTemplateTable) models.NotificacaoTemplate {
	output := models.NotificacaoTemplate{}
	output.ID = input.Id
	output.Slugname = input.Slugname
	output.Nome = input.Nome
	output.Subject = input.Subject
	output.Body = input.Body
	output.SmsBody = input.SmsBody
	return output
}
