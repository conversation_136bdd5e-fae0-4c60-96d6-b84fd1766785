package input

import (
	"agilize/src/application/declaracao/src/domain/models"
	"agilize/src/drivers/datastores/accounting_acessor"
	"context"
	"time"
)

type AccountingAcessorRepository interface {
	FetchPrefeituraCredential(ctx context.Context, cnpj string) (models.Credential, error)
	FetchNfeByCnpj(ctx context.Context, cnpj string, competence time.Time) ([]models.Nfse, error)
	FetchArquivoFiscalEmpresa(ctx context.Context, acessorParams accounting_acessor.ArquivoFiscalParams) (models.ArquivoFiscalContent, error)
	GetDeclaracaoIsAutomatica(ctx context.Context, cnpj, declaracaoSlugname, competencia string) (bool, error)
	CriarImpostoMit(ctx context.Context, cnpj, competence string) error
	CompleteMit(ctx context.Context, cnpj, competence string) error
	FetchImpostosAPagar(ctx context.Context, cnpj, competence string) (models.ImpostosAPagar, error)
}

type DeclaracaoRepository interface {
	CreateDeclaracaoEmpresa(ctx context.Context, declaracao models.DeclaracaoEmpresa) (models.DeclaracaoEmpresa, error)
	FindDeclaracao(ctx context.Context, slugname string) (models.Declaracao, error)
	FindCompany(ctx context.Context, cnpj string) (models.Company, error)
	FindCompanyById(ctx context.Context, id string) (models.Company, error)
	DeclaracaoRealizada(ctx context.Context, id string) error
	FindDeclaracaoEmpresa(ctx context.Context, declaracaoId string, companyId string, competence time.Time) (models.DeclaracaoEmpresa, error)
	FindDeclaracaoEmpresaByProcessInstance(ctx context.Context, processInstanceId string) (models.DeclaracaoEmpresa, error)
	UpdateDeclaracaoProcessInstance(ctx context.Context, declaracaoEmpresaId string, processInstanceId string) error
}

type DeclaracaoStatusReportRepository interface {
	FindDeclaracaoStatusReportByDeclaracaoSlugname(ctx context.Context, declaracaoSlugname string) (models.DeclaracaoStatusReport, error)
	UpdateDeclaracaoStatusReportIndisponivel(ctx context.Context, declaracaoReportId string) error
	UpdateDeclaracaoStatusReportDisponivel(ctx context.Context, declaracaoReportId string) error
}
