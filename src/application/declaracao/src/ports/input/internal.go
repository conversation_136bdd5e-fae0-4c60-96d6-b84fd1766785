package input

import (
	"agilize/src/application/declaracao/src/domain/models"
	"agilize/src/shared/logger"
	"context"
)

type EnviarDeclaracoesVariables interface {
	GetMes() int
	GetAno() int
	GetCnpj() string
	GetRazaoSocial() string
	GetInscricaoMunicipal() string
	IsDeclaracaoMarcadaComoManual() bool
	MarcarComoManual()
	MarcarComoArquivada(observacao string)
	IsDeclaracaoMarcadaComoArquivada() bool
	IsSimplesNacional() bool
	IsLucroPresumido() bool
	IsMei() bool
	MarcarComoCredencialInvalida()
	IsDeclaracaoMarcadaComoCredencialInvalida() bool
	MarcarComoPrefeituraIndisponivel()
	IsDeclaracaoMarcadaComoPrefeituraIndisponivel() bool
}

type EnviarDeclaracoesVariablesHealthCheck interface {
	GetDeclaracaoVariables() *models.DeclaracaoVariables
	GetIsHealth() bool
	MarcarComoHealth()
	MarcarComoUnHealth()
}

type Declaracao interface {
	EnviarDeclaracao(EnviarDeclaracoesVariables) error
	HealthCheck(EnviarDeclaracoesVariablesHealthCheck) error
	GetRecibo() []byte
	GetGuiaPagamento() []byte
	GetReciboExtension() string
	GetGuiaPagamentoExtension() string
}

type DeclaracoesService interface {
	CriarDeclaracaoEmpresa(ctx context.Context, declaracao models.DeclaracaoEmpresa) (models.DeclaracaoEmpresa, error)
	GetDeclaracao(ctx context.Context, slugname string) (models.Declaracao, error)
	DeclaracaoEmpresaRealizada(ctx context.Context, declaracaoEmpresaId string) error
	GetDeclaracaoStatusReportByDeclaracaoSlugname(ctx context.Context, declaracaoSlugname string) (models.DeclaracaoStatusReport, error)
	DefinirComoPrefeituraIndisponivel(ctx context.Context, declaracaoReportId string) error
	DefinirComoPrefeituraDisponivel(ctx context.Context, declaracaoReportId string) error
	GetDeclaracaoIsAutomatica(ctx context.Context, declaracao models.DeclaracaoEmpresa) (bool, error)
}

type CrawlerDeclaracao interface {
	GetCrawlerDeclaracao(ibgeCode string, issGissOnline bool, logger logger.Logger) interface{}
}

type DeclaracaoEmpresa interface {
	IsGissOnline() bool
	MarcarComoManual()
	IsMarcadaComoCredencialInvalida() bool
	IsPrefeituraIndiponivel() bool
}
