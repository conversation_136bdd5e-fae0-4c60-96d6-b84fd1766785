package services

import (
	"agilize/src/application/declaracao/src/domain/models"
	"agilize/src/application/declaracao/src/ports/input"
	models2 "agilize/src/application/processos/src/domain/models"
	"agilize/src/shared/logger"
	"context"
	"fmt"
)

type DeclaracaoService struct {
	declaracaoRepository             input.DeclaracaoRepository
	declaracaoStatusReportRepository input.DeclaracaoStatusReportRepository
	accountingAcessorRepository      input.AccountingAcessorRepository
	crawlerDeclaracao                input.CrawlerDeclaracao
}

func NewDeclaracaoService(
	declaracaoRepository input.DeclaracaoRepository,
	declaracaoStatusReportRepository input.DeclaracaoStatusReportRepository,
	accountingAcessorRepository input.AccountingAcessorRepository,
	crawlerDeclaracao input.CrawlerDeclaracao,
) input.DeclaracoesService {
	return &DeclaracaoService{
		declaracaoRepository:             declaracaoRepository,
		declaracaoStatusReportRepository: declaracaoStatusReportRepository,
		accountingAcessorRepository:      accountingAcessorRepository,
		crawlerDeclaracao:                crawlerDeclaracao,
	}
}

func (d *DeclaracaoService) CriarDeclaracaoEmpresa(ctx context.Context, declaracaoEmpresa models.DeclaracaoEmpresa) (models.DeclaracaoEmpresa, error) {
	if declaracaoEmpresa.Cnpj == "" {
		return models.DeclaracaoEmpresa{}, fmt.Errorf("cnpj é obrigatório - **********")
	}
	declaracao, err := d.declaracaoRepository.FindDeclaracao(ctx, declaracaoEmpresa.DeclaracaoSlugname)
	if err != nil {
		return models.DeclaracaoEmpresa{}, err
	}

	company, err := d.declaracaoRepository.FindCompany(ctx, declaracaoEmpresa.Cnpj)
	if err != nil {
		return models.DeclaracaoEmpresa{}, err
	}

	if company.TenantID == "" {
		return models.DeclaracaoEmpresa{}, fmt.Errorf("tenant é obrigatório - **********")
	}

	declaracaoEmpresa.DeclaracaoID = declaracao.ID
	declaracaoEmpresa.CompanyID = company.ID
	declaracaoEmpresa.TenantID = company.TenantID

	declaracaoEmpresaAnterior, err := d.declaracaoRepository.FindDeclaracaoEmpresa(ctx, declaracaoEmpresa.DeclaracaoID, declaracaoEmpresa.CompanyID, declaracaoEmpresa.Competence)

	if err != nil {
		return models.DeclaracaoEmpresa{}, err
	}
	if declaracaoEmpresaAnterior.ID != "" {
		declaracaoEmpresa.ID = declaracaoEmpresaAnterior.ID
		if declaracaoEmpresa.ProcessInstanceID != "" && declaracaoEmpresaAnterior.ProcessInstanceID == "" {
			err = d.declaracaoRepository.UpdateDeclaracaoProcessInstance(ctx, declaracaoEmpresa.ID, declaracaoEmpresa.ProcessInstanceID)
		}
		return declaracaoEmpresa, err
	}

	declaracaoEmpresa, err = d.declaracaoRepository.CreateDeclaracaoEmpresa(ctx, declaracaoEmpresa)
	if err != nil {
		return models.DeclaracaoEmpresa{}, err
	}
	return declaracaoEmpresa, nil
}

func (d *DeclaracaoService) GetDeclaracao(ctx context.Context, slugname string) (models.Declaracao, error) {
	declaracao, err := d.declaracaoRepository.FindDeclaracao(ctx, slugname)
	if err != nil {
		return declaracao, err
	}
	return declaracao, nil
}

func (d *DeclaracaoService) DeclaracaoEmpresaRealizada(ctx context.Context, declaracaoEmpresaId string) error {
	err := d.declaracaoRepository.DeclaracaoRealizada(ctx, declaracaoEmpresaId)
	if err != nil {
		return err
	}
	return nil
}

func (d *DeclaracaoService) GetDeclaracaoStatusReportByDeclaracaoSlugname(ctx context.Context, declaracaoSlugname string) (models.DeclaracaoStatusReport, error) {
	report, err := d.declaracaoStatusReportRepository.FindDeclaracaoStatusReportByDeclaracaoSlugname(ctx, declaracaoSlugname)
	if err != nil {
		return report, err
	}
	return report, nil
}

func (d *DeclaracaoService) DefinirComoPrefeituraIndisponivel(ctx context.Context, declaracaoReportId string) error {
	return d.declaracaoStatusReportRepository.UpdateDeclaracaoStatusReportIndisponivel(ctx, declaracaoReportId)
}

func (d *DeclaracaoService) DefinirComoPrefeituraDisponivel(ctx context.Context, declaracaoReportId string) error {
	return d.declaracaoStatusReportRepository.UpdateDeclaracaoStatusReportDisponivel(ctx, declaracaoReportId)
}

func (d *DeclaracaoService) GetDeclaracaoIsAutomatica(ctx context.Context, declaracaoEmpresa models.DeclaracaoEmpresa) (bool, error) {
	if declaracaoEmpresa.IsMarcadaComoManual {
		return false, nil
	}

	switch declaracaoEmpresa.ProcessKey {
	case models2.DECLARACOES_DCTF, models2.DECLARACOES_SPED:
		return d.accountingAcessorRepository.GetDeclaracaoIsAutomatica(
			ctx,
			declaracaoEmpresa.Cnpj,
			declaracaoEmpresa.DeclaracaoSlugname,
			declaracaoEmpresa.Competence.Format("2006-01-02T15:04:05P"),
		)
	case models2.DECLARACOES_MUNICIPAIS, models2.DECLARACOES_MUNICIPAIS_GISS_ONLINE:
		return d.IsDeclaracaoMunicipalAutomatica(declaracaoEmpresa)
	}
	return true, nil
}

func (d *DeclaracaoService) IsDeclaracaoMunicipalAutomatica(declaracaoEmpresa models.DeclaracaoEmpresa) (bool, error) {
	_, ok := d.crawlerDeclaracao.GetCrawlerDeclaracao(
		declaracaoEmpresa.CityCode,
		declaracaoEmpresa.IsGissOnline(),
		logger.GetInfoLogger()).(input.Declaracao)
	return ok, nil
}
