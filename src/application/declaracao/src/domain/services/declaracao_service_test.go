package services_test

import (
	declaracaoMocks "agilize/mocks/declaracao"
	"agilize/src/application/declaracao/src/domain/models"
	"agilize/src/application/declaracao/src/domain/services"
	"agilize/src/shared/cryptus/uuid"
	"agilize/src/shared/testify"
	"agilize/src/shared/testify/assert"
	"context"
	"testing"
	"time"

	"go.uber.org/mock/gomock"
)

func TestDeclaracaoService(ts *testing.T) {
	ctrl := gomock.NewController(ts)
	defer ctrl.Finish()
	ctx := context.Background()
	testify.It(ts, "Deve criar declaração no banco de dados", func(tt *testing.T) {

		declaracaoModel := models.Declaracao{ID: uuid.NewV4()}
		companyModel := models.Company{ID: uuid.NewV4(), TenantID: uuid.NewV4()}

		declaracaoEmpresa := models.DeclaracaoEmpresa{
			Cnpj:               "**************",
			CityCode:           "********",
			Competence:         time.Date(2023, 12, 1, 0, 0, 0, 0, time.Local),
			InscricaoMunicipal: "*********",
		}

		declaracaoRepo := declaracaoMocks.NewMockDeclaracaoRepository(ctrl)
		declaracaoRepo.EXPECT().FindCompany(gomock.Any(), gomock.Any()).Return(companyModel, nil)
		declaracaoRepo.EXPECT().FindDeclaracao(gomock.Any(), gomock.Any()).Return(declaracaoModel, nil)
		declaracaoRepo.EXPECT().CreateDeclaracaoEmpresa(gomock.Any(), gomock.Any()).Return(declaracaoEmpresa, nil)
		declaracaoRepo.EXPECT().FindDeclaracaoEmpresa(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(declaracaoEmpresa, nil)

		declaracaoStatusReportRepo := declaracaoMocks.NewMockDeclaracaoStatusReportRepository(ctrl)
		declaracaoCrawler := declaracaoMocks.NewMockCrawlerDeclaracao(ctrl)

		service := services.NewDeclaracaoService(declaracaoRepo, declaracaoStatusReportRepo, declaracaoMocks.NewMockAccountingAcessorRepository(ctrl), declaracaoCrawler)
		declaracao, err := service.CriarDeclaracaoEmpresa(ctx, declaracaoEmpresa)

		assert.Nil(tt, err)
		assert.NotNil(tt, declaracao.ID)
		assert.NotNil(tt, declaracao.DeclaracaoID)

	})

	testify.It(ts, "Caso não tenha o CNPJ deve retornar um erro.", func(tt *testing.T) {

		declaracaoEmpresa := models.DeclaracaoEmpresa{
			CityCode:           "********",
			Competence:         time.Date(2023, 12, 1, 0, 0, 0, 0, time.Local),
			InscricaoMunicipal: "*********",
		}

		declaracaoRepo := declaracaoMocks.NewMockDeclaracaoRepository(ctrl)
		declaracaoStatusReportRepo := declaracaoMocks.NewMockDeclaracaoStatusReportRepository(ctrl)

		repos := declaracaoMocks.NewMockDeclaracaoRepositoryContainer(ctrl)
		repos.EXPECT().DeclaracaoRepository().Return(declaracaoRepo)
		repos.EXPECT().DeclaracaoStatusReportRepository().Return(declaracaoStatusReportRepo)
		declaracaoCrawler := declaracaoMocks.NewMockCrawlerDeclaracao(ctrl)

		service := services.NewDeclaracaoService(repos.DeclaracaoRepository(), repos.DeclaracaoStatusReportRepository(), declaracaoMocks.NewMockAccountingAcessorRepository(ctrl), declaracaoCrawler)
		_, err := service.CriarDeclaracaoEmpresa(ctx, declaracaoEmpresa)

		assert.NotNil(tt, err)
		assert.Equal(tt, err.Error(), "cnpj é obrigatório - **********")
	})

	testify.It(ts, "Caso não tenha o Tenant deve retornar um erro.", func(tt *testing.T) {

		declaracaoEmpresa := models.DeclaracaoEmpresa{
			Cnpj:               "**************",
			CityCode:           "*********",
			Competence:         time.Date(2023, 12, 1, 0, 0, 0, 0, time.Local),
			InscricaoMunicipal: "*********",
		}
		companyModel := models.Company{ID: uuid.NewV4()}
		declaracaoModel := models.Declaracao{ID: uuid.NewV4()}

		declaracaoRepo := declaracaoMocks.NewMockDeclaracaoRepository(ctrl)
		declaracaoStatusReportRepo := declaracaoMocks.NewMockDeclaracaoStatusReportRepository(ctrl)

		repos := declaracaoMocks.NewMockDeclaracaoRepositoryContainer(ctrl)
		repos.EXPECT().DeclaracaoRepository().Return(declaracaoRepo)
		repos.EXPECT().DeclaracaoStatusReportRepository().Return(declaracaoStatusReportRepo)
		declaracaoRepo.EXPECT().FindDeclaracao(gomock.Any(), gomock.Any()).Return(declaracaoModel, nil)
		declaracaoRepo.EXPECT().FindCompany(gomock.Any(), gomock.Any()).Return(companyModel, nil)
		declaracaoCrawler := declaracaoMocks.NewMockCrawlerDeclaracao(ctrl)

		service := services.NewDeclaracaoService(repos.DeclaracaoRepository(), repos.DeclaracaoStatusReportRepository(), declaracaoMocks.NewMockAccountingAcessorRepository(ctrl), declaracaoCrawler)
		_, err := service.CriarDeclaracaoEmpresa(ctx, declaracaoEmpresa)

		assert.NotNil(tt, err)
		assert.Equal(tt, err.Error(), "tenant é obrigatório - **********")
	})
}
