package models

type DeclaracaoVariablesHealthCheck struct {
	DeclaracaoVariables *DeclaracaoVariables
	IsHealth            bool
}

func (dvh *DeclaracaoVariablesHealthCheck) GetDeclaracaoVariables() *DeclaracaoVariables {
	return dvh.DeclaracaoVariables
}

func (dvh *DeclaracaoVariablesHealthCheck) GetIsHealth() bool {
	return dvh.IsHealth
}

func (dvh *DeclaracaoVariablesHealthCheck) MarcarComoHealth() {
	dvh.IsHealth = true
}

func (dvh *DeclaracaoVariablesHealthCheck) MarcarComoUnHealth() {
	dvh.IsHealth = false
}
