package models

import (
	"agilize/src/application/artefatos/src/domain/models"
	"agilize/src/shared/constantes"
	"agilize/src/shared/datetime"
	"agilize/src/shared/types/customdate"
	"errors"
	"fmt"
	"strings"
	"time"
)

const (
	LUCRO_PRESUMIDO                = "lucro_presumido"
	SIMPLES_NACIONAL               = "simples_nacional"
	MEI                            = "mei"
	OBSERVACAO_CREDENCIAL_INVALIDA = "empresa não possui credencial válida"
	DCTF_SLUGNAME                  = "dctf"
	SPED_SLUGNAME                  = "sped"
	EFD_ICMS_IPI_SLUGNAME          = "efd_icms_ipi"
)

var gissOnlineIbgeCodes = []string{
	constantes.SP_GUARULHOS,
	constantes.SP_SAO_BERNARDO_DO_CAMPO,
	constantes.SP_SANTO_ANDRE,
	constantes.MG_CONTAGEM,
	constantes.SP_SAO_CAETANO_DO_SUL,
	constantes.MG_BETIM,
	constantes.SP_DIADEMA,
	constantes.SP_MAUA,
	constantes.SP_ARARAQUARA,
	constantes.SP_HORTOLANDIA,
	constantes.SP_GUARUJA,
	constantes.SP_SUZANO,
	constantes.SP_RIO_CLARO,
	constantes.SP_PAULINIA,
	constantes.SP_RIBEIRAO_PIRES,
	constantes.PE_CARUARU,
	constantes.SP_CAPIVARI,
}

type Variables struct {
	Key   string
	Value string
}

type Company struct {
	ID       string
	TenantID string
	Cnpj     string
}

type DeclaracaoEmpresa struct {
	ID                       string
	TenantID                 string
	CompanyID                string
	DeclaracaoID             string
	Cnpj                     string
	RazaoSocial              string
	QuantidadeTentativas     string
	CityCode                 string
	Competence               time.Time
	InscricaoMunicipal       string
	Documentos               []Documento
	TemGuiaPagamento         bool
	ProcessInstanceID        string
	IsMarcadaComoManual      bool
	IsMarcadaComoArquivada   bool
	PossuiCredencialInvalida bool
	IsPrefeituraIndisponivel bool
	RegimeTributario         string
	Observacao               string
	DeclaracaoSlugname       string
	ProcessKey               string
}

type Documento struct {
	Content              []byte
	Extension            string
	TipoArtefatoSlugname models.TipoArtefatoSlugname
}

type Declaracao struct {
	ID          string
	Description string
	Slugname    string
	DeadLineDay int
}

type Nfse struct {
	Num                       int64
	Competence                string
	Amount                    int64
	IsPisRetido               bool
	IsServicoPrestadoExterior bool
	BaseCalculo               int64
	RetainedAmount            int64
	DataEmissao               time.Time
	TemIssRetido              bool
	ValorDeducoes             int64
	IssuedAt                  string
	Status                    string
	TomadorNome               string
	ValorServico              int64
	CnaeCode                  string
	AliquotaIss               int64
	ValorIss                  int64
	ValorIr                   int64
	ValorPis                  int64
	ValorCofins               int64
	ValorCsll                 int64
	ValorInss                 int64
	Id                        string
	Anexo                     string
}

type DeclaracaoVariables struct {
	Mes                      int
	Ano                      int
	Cnpj                     string
	RazaoSocial              string
	InscricacaoMunicipal     string
	IsMarcadaComoManual      bool
	IsMarcadaComoArquivada   bool
	PossuiCredencialInvalida bool
	IsPrefeituraIndisponivel bool
	RegimeTributario         string
	Observacao               string
}

type DeclaracaoStatusReport struct {
	ID                 string
	Status             string
	IbgeCode           string
	ProximaTentativaEm *time.Time
	UltimaTentativaEm  *time.Time
}

type ArquivoFiscalContent struct {
	Content   string `json:"content"`
	Extension string `json:"extension"`
}

type ArquivoFiscalError struct {
	Error error
}

func (d *Declaracao) PassouDoPrazo(competencia string) (bool, error) {
	switch d.Slugname {
	case DCTF_SLUGNAME, SPED_SLUGNAME:
		competence, err := customdate.TryParse(competencia)
		if err != nil {
			return false, fmt.Errorf("competência inválida: %s", err)
		}

		deadline := datetime.AdicionarMeses(competence.Time, 2)
		deadline = datetime.AdicionarDias(deadline, d.DeadLineDay-1)
		datetime.ToEndOfDay(&deadline)
		return !datetime.IsFuture(&deadline), nil
	default:
		return false, nil
	}
}

func (dv *DeclaracaoVariables) GetMes() int {
	return dv.Mes
}

func (dv *DeclaracaoVariables) GetAno() int {
	return dv.Ano
}

func (dv *DeclaracaoVariables) GetRazaoSocial() string {
	return dv.RazaoSocial
}

func (dv *DeclaracaoVariables) GetCnpj() string {
	return dv.Cnpj
}

func (dv *DeclaracaoVariables) GetRegimeTributario() (string, error) {
	regime := dv.RegimeTributario
	if regime == "" {
		return "", errors.New("regime tributário invalido")
	}
	return dv.RegimeTributario, nil
}

func (dv *DeclaracaoVariables) IsSimplesNacional() bool {
	regime, _ := dv.GetRegimeTributario()
	return regime == SIMPLES_NACIONAL
}

func (dv *DeclaracaoVariables) IsLucroPresumido() bool {
	regime, _ := dv.GetRegimeTributario()
	return regime == LUCRO_PRESUMIDO
}

func (dv *DeclaracaoVariables) IsMei() bool {
	regime, _ := dv.GetRegimeTributario()
	return regime == MEI
}

func (dv *DeclaracaoVariables) GetInscricaoMunicipal() string {
	return dv.InscricacaoMunicipal
}

func (dv *DeclaracaoVariables) MarcarComoManual() {
	dv.IsMarcadaComoManual = true
}

func (dv *DeclaracaoVariables) IsDeclaracaoMarcadaComoManual() bool {
	return dv.IsMarcadaComoManual
}

func (dv *DeclaracaoVariables) MarcarComoArquivada(observacao string) {
	dv.IsMarcadaComoArquivada = true
	dv.Observacao = observacao
}

func (dv *DeclaracaoVariables) IsDeclaracaoMarcadaComoArquivada() bool {
	return dv.IsMarcadaComoArquivada
}

func (dv *DeclaracaoVariables) MarcarComoCredencialInvalida() {
	dv.PossuiCredencialInvalida = true
	dv.Observacao = OBSERVACAO_CREDENCIAL_INVALIDA
}

func (dv *DeclaracaoVariables) IsDeclaracaoMarcadaComoCredencialInvalida() bool {
	return dv.PossuiCredencialInvalida == true && dv.Observacao == OBSERVACAO_CREDENCIAL_INVALIDA
}

func (dv *DeclaracaoVariables) MarcarComoPrefeituraIndisponivel() {
	dv.IsPrefeituraIndisponivel = true
}

func (dv *DeclaracaoVariables) IsDeclaracaoMarcadaComoPrefeituraIndisponivel() bool {
	return dv.IsPrefeituraIndisponivel == true
}

func (de *DeclaracaoEmpresa) MarcarComoManual() {
	de.IsMarcadaComoManual = true
}

func (de *DeclaracaoEmpresa) IsGissOnline() bool {
	for _, code := range gissOnlineIbgeCodes {
		if code == de.CityCode {
			return true
		}
	}

	return false
}

func (de *DeclaracaoEmpresa) IsMarcadaComoCredencialInvalida() bool {
	return de.PossuiCredencialInvalida == true && de.Observacao == OBSERVACAO_CREDENCIAL_INVALIDA
}

func (de *DeclaracaoEmpresa) IsPrefeituraIndiponivel() bool {
	return de.IsPrefeituraIndisponivel == true
}

func (afe *ArquivoFiscalError) GetUserError() error {
	switch true {
	case strings.Contains(strings.ToLower(afe.Error.Error()), "empresa sem senior partner"):
		return errors.New("faltam dados do sócio responsável")
	default:
		return errors.New("erro ao fazer download do arquivo fiscal")
	}
}
