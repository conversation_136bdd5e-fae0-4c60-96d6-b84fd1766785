package models

import (
	"testing"
	"time"
)

const (
	testDCTFSlugname = "dctf"
	testSPEDSlugname = "sped"
)

func TestPassouDoPrazo(t *testing.T) {
	testCases := []struct {
		name          string
		declaracao    Declaracao
		competencia   string
		passouDoPrazo bool
		esperadoErro  bool
	}{
		{
			name: "DCTF - Competência recente",
			declaracao: Declaracao{
				Slugname:    testDCTFSlugname,
				DeadLineDay: 10,
			},
			competencia:   time.Now().AddDate(0, -1, 0).Format("2006-01"),
			passouDoPrazo: false,
			esperadoErro:  false,
		},
		{
			name: "DCTF - Competência antiga",
			declaracao: Declaracao{
				Slugname:    testDCTFSlugname,
				DeadLineDay: 10,
			},
			competencia:   time.Now().AddDate(0, -3, 0).Format("2006-01"),
			passouDoPrazo: true,
			esperadoErro:  false,
		},
		{
			name: "SPED - Competência recente",
			declaracao: Declaracao{
				Slugname:    testSPEDSlugname,
				DeadLineDay: 10,
			},
			competencia:   time.Now().AddDate(0, -1, 0).Format("2006-01"),
			passouDoPrazo: false,
			esperadoErro:  false,
		},
		{
			name: "SPED - Competência antiga",
			declaracao: Declaracao{
				Slugname:    testSPEDSlugname,
				DeadLineDay: 10,
			},
			competencia:   time.Now().AddDate(0, -3, 0).Format("2006-01"),
			passouDoPrazo: true,
			esperadoErro:  false,
		},
		{
			name: "DCTF - Data limite",
			declaracao: Declaracao{
				Slugname:    testDCTFSlugname,
				DeadLineDay: 10,
			},
			competencia:   time.Now().AddDate(0, 2, 0).Format("2006-01"),
			passouDoPrazo: false,
			esperadoErro:  false,
		},
		{
			name:        "SPED - 1 dia após o prazo",
			competencia: time.Now().AddDate(0, -2, -1).Format("2006-01"),
			declaracao: Declaracao{
				Slugname:    testSPEDSlugname,
				DeadLineDay: 1,
			},
			passouDoPrazo: true,
			esperadoErro:  false,
		},
		{
			name: "Outro tipo de declaração",
			declaracao: Declaracao{
				Slugname:    "outro_tipo",
				DeadLineDay: 6,
			},
			competencia:   time.Now().AddDate(0, -3, 0).Format("2006-01"),
			passouDoPrazo: false,
			esperadoErro:  false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			resultado, err := tc.declaracao.PassouDoPrazo(tc.competencia)

			if (err != nil) != tc.esperadoErro {
				t.Errorf("PassouDoPrazo() erro = %v, esperadoErro = %v", err, tc.esperadoErro)
				return
			}

			if resultado != tc.passouDoPrazo {
				t.Errorf("PassouDoPrazo() = %v, esperado = %v", resultado, tc.passouDoPrazo)
			}
		})
	}
}
