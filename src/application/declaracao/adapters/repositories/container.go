package repositories

import (
	"agilize/src/application/declaracao/src/ports/input"
	"agilize/src/drivers/datastores"
	"sync"
)

var once sync.Once

var accountingAcessorRepository input.AccountingAcessorRepository
var declaracaoRepository input.DeclaracaoRepository
var declaracaoStatusReportRepository input.DeclaracaoStatusReportRepository

type DeclaracaoRepositoryContainer interface {
	AccountingAcessorRepository() input.AccountingAcessorRepository
	DeclaracaoRepository() input.DeclaracaoRepository
	DeclaracaoStatusReportRepository() input.DeclaracaoStatusReportRepository
}

type container struct {
	accountingAcessorRepository      input.AccountingAcessorRepository
	declaracaoRepository             input.DeclaracaoRepository
	declaracaoStatusReportRepository input.DeclaracaoStatusReportRepository
}

func NewDeclaracaoRepositoryContainer(ds datastores.DataStore) DeclaracaoRepositoryContainer {
	once.Do(func() {
		accountingAcessorRepository = NewAccountingAcessorRepository(ds.Accounting())
		declaracaoRepository = NewDeclaracaoRepository(ds.Postgres())
		declaracaoStatusReportRepository = NewDeclaracaoStatusReportRepository(ds.Postgres())
	})

	return container{
		accountingAcessorRepository:      accountingAcessorRepository,
		declaracaoRepository:             declaracaoRepository,
		declaracaoStatusReportRepository: declaracaoStatusReportRepository,
	}
}

func (c container) AccountingAcessorRepository() input.AccountingAcessorRepository {
	return c.accountingAcessorRepository
}

func (c container) DeclaracaoRepository() input.DeclaracaoRepository {
	return c.declaracaoRepository
}

func (c container) DeclaracaoStatusReportRepository() input.DeclaracaoStatusReportRepository {
	return c.declaracaoStatusReportRepository
}
