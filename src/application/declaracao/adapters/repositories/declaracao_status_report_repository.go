package repositories

import (
	"agilize/src/application/declaracao/adapters/converters"
	"agilize/src/application/declaracao/adapters/dto"
	"agilize/src/application/declaracao/src/domain/models"
	"agilize/src/application/declaracao/src/ports/input"
	"agilize/src/drivers/datastores/db"
	"context"
	"time"
)

type DeclaracaoStatusReportRepository struct {
	store     db.SqlDataStore
	converter converters.DeclaracaoStatusReportConverter
}

func NewDeclaracaoStatusReportRepository(store db.SqlDataStore) input.DeclaracaoStatusReportRepository {
	return &DeclaracaoStatusReportRepository{
		store:     store,
		converter: converters.NewDeclaracaoStatusReportConverter(),
	}
}

func (dr *DeclaracaoStatusReportRepository) FindDeclaracaoStatusReportByDeclaracaoSlugname(ctx context.Context, declaracaoSlugname string) (models.DeclaracaoStatusReport, error) {
	var declaracaoStatusReportTable dto.DeclaracaoStatusReportTable

	q := `SELECT dsr.id, dsr.declaracao_id, dsr.status, dsr.proxima_tentativa_em, dsr.ultima_tentativa_em FROM declaracoes_status_report dsr
            inner join declaracoes d on d.id = dsr.declaracao_id
            WHERE d.slugname = $1`
	err := dr.store.WithContext(ctx).QueryOne(
		q,
		&declaracaoStatusReportTable,
		declaracaoSlugname,
	)
	return dr.converter.FromTableToModel(declaracaoStatusReportTable), err
}

func (dr *DeclaracaoStatusReportRepository) UpdateDeclaracaoStatusReportIndisponivel(ctx context.Context, declaracaoReportId string) error {
	horarioAtual := time.Now()
	horarioProximaTentativa := horarioAtual.Add(time.Hour)
	q := "UPDATE declaracoes_status_report SET status = 'indisponivel', proxima_tentativa_em = $3, ultima_tentativa_em = $2 WHERE id = $1"
	err := dr.store.WithContext(ctx).Exec(
		q,
		declaracaoReportId,
		horarioAtual,
		horarioProximaTentativa,
	)
	return err
}

func (dr *DeclaracaoStatusReportRepository) UpdateDeclaracaoStatusReportDisponivel(ctx context.Context, declaracaoReportId string) error {
	q := "UPDATE declaracoes_status_report SET status = 'disponivel', proxima_tentativa_em=null, ultima_tentativa_em = null WHERE id = $1"
	err := dr.store.WithContext(ctx).Exec(
		q,
		declaracaoReportId,
	)
	return err
}
