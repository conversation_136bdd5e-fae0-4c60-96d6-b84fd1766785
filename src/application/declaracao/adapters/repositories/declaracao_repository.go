package repositories

import (
	"agilize/src/application/declaracao/adapters/converters"
	"agilize/src/application/declaracao/adapters/dto"
	"agilize/src/application/declaracao/src/domain/models"
	"agilize/src/application/declaracao/src/ports/input"
	"agilize/src/drivers/datastores/db"
	"context"
	_ "embed"
	"errors"
	"strings"
	"time"
)

//go:embed sql/declaracao_empresa_by_process_instance.sql
var declaracaoEmpresaByProcessInstance string

type DeclaracaoRepository struct {
	store     db.SqlDataStore
	converter converters.DeclaracaoConverter
}

func NewDeclaracaoRepository(store db.SqlDataStore) input.DeclaracaoRepository {
	return &DeclaracaoRepository{
		store:     store,
		converter: converters.NewDeclaracaoConverter(),
	}
}

func (dr *DeclaracaoRepository) FindDeclaracaoEmpresa(ctx context.Context, declaracaoId string, companyId string, competence time.Time) (models.DeclaracaoEmpresa, error) {
	var declaracaoEmpresaTable dto.DeclaracaoEmpresaTable

	q := "SELECT id, declaracao_id, company_id, competence, process_instance_id, created_at, updated_at FROM declaracoes_empresa WHERE declaracao_id = $1 AND company_id = $2 and competence = $3"
	err := dr.store.WithContext(ctx).QueryOne(
		q,
		&declaracaoEmpresaTable,
		declaracaoId,
		companyId,
		competence,
	)

	if err != nil && !strings.Contains(err.Error(), "no rows in result set") {
		return models.DeclaracaoEmpresa{}, err
	}

	if declaracaoEmpresaTable.ID == "" {
		return models.DeclaracaoEmpresa{}, nil
	}

	return dr.converter.FromDeclaracaoEmpresaTableToDeclaracaoEmpresaModel(declaracaoEmpresaTable), nil
}

func (dr *DeclaracaoRepository) FindDeclaracaoEmpresaByProcessInstance(ctx context.Context, processInstanceId string) (models.DeclaracaoEmpresa, error) {
	var declaracaoEmpresaTable dto.DeclaracaoEmpresaTable

	err := dr.store.WithContext(ctx).QueryOne(
		declaracaoEmpresaByProcessInstance,
		&declaracaoEmpresaTable,
		processInstanceId,
	)

	if err != nil && strings.Contains(err.Error(), "no rows in result set") {
		return models.DeclaracaoEmpresa{}, nil
	}

	if err != nil {
		return models.DeclaracaoEmpresa{}, errors.New(err.Error() + " - 1709839495")
	}

	if declaracaoEmpresaTable.ID == "" {
		return models.DeclaracaoEmpresa{}, errors.New("declaração empresa não encontrada - 1709839494")
	}

	return dr.converter.FromDeclaracaoEmpresaTableToDeclaracaoEmpresaModel(declaracaoEmpresaTable), nil
}

func (dr *DeclaracaoRepository) CreateDeclaracaoEmpresa(ctx context.Context, item models.DeclaracaoEmpresa) (models.DeclaracaoEmpresa, error) {

	table, _ := dr.converter.FromModelToTable(item)

	q := "INSERT INTO declaracoes_empresa (id, declaracao_id, company_id, process_instance_id, competence, closing_date,  created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)"
	err := dr.store.WithContext(ctx).Exec(
		q,
		table.ID,
		table.DeclaracaoID,
		table.CompanyID,
		table.ProcessInstanceID,
		table.Competence,
		nil,
		table.CreatedAt,
		table.UpdatedAt,
	)
	item.ID = table.ID
	return item, err
}

func (dr *DeclaracaoRepository) FindDeclaracao(ctx context.Context, slugname string) (models.Declaracao, error) {
	var declaracaoTable dto.DeclaracaoTable

	q := "SELECT id, description, slugname, deadline_day, created_at, updated_at  FROM declaracoes WHERE slugname = $1"
	err := dr.store.WithContext(ctx).QueryOne(
		q,
		&declaracaoTable,
		slugname,
	)
	return dr.converter.FromDeclaracaoTableToDeclaracaoModel(declaracaoTable), err
}

func (dr *DeclaracaoRepository) FindCompany(ctx context.Context, cnpj string) (models.Company, error) {
	var partiesTable dto.PartiesTable

	q := "SELECT id, tenant_id FROM parties WHERE cnpj = $1"
	err := dr.store.WithContext(ctx).QueryOne(
		q,
		&partiesTable,
		cnpj,
	)
	return dr.converter.FromTablePartiesToModelCompany(partiesTable), err
}

func (dr *DeclaracaoRepository) FindCompanyById(ctx context.Context, id string) (models.Company, error) {
	var partiesTable dto.PartiesTable

	q := "SELECT id, tenant_id, cnpj FROM parties WHERE id = $1"
	err := dr.store.WithContext(ctx).QueryOne(
		q,
		&partiesTable,
		id,
	)
	return dr.converter.FromTablePartiesToModelCompany(partiesTable), err
}

func (dr *DeclaracaoRepository) DeclaracaoRealizada(ctx context.Context, declaracaoEmpresaId string) error {
	q := "UPDATE declaracoes_empresa SET closing_date = $1 WHERE id = $2"
	err := dr.store.WithContext(ctx).Exec(
		q,
		time.Now(),
		declaracaoEmpresaId,
	)
	return err
}

func (dr *DeclaracaoRepository) UpdateDeclaracaoProcessInstance(ctx context.Context, declaracaoEmpresaId string, processInstanceId string) error {
	q := "UPDATE declaracoes_empresa SET process_instance_id = $1 WHERE id = $2"
	err := dr.store.WithContext(ctx).Exec(
		q,
		processInstanceId,
		declaracaoEmpresaId,
	)
	return err
}
