package repositories

import (
	"agilize/src/application/declaracao/adapters/converters"
	"agilize/src/application/declaracao/adapters/dto"
	"agilize/src/application/declaracao/src/domain/models"
	"agilize/src/application/declaracao/src/ports/input"
	"agilize/src/drivers/datastores/accounting_acessor"
	"context"
	"time"
)

type AccountingAcessorRepository struct {
	store     accounting_acessor.AccountingStore
	converter converters.AccountingAcessorConverter
}

func NewAccountingAcessorRepository(store accounting_acessor.AccountingStore) input.AccountingAcessorRepository {
	return &AccountingAcessorRepository{
		store:     store,
		converter: converters.NewAccountingAcessorConverter(),
	}
}

func (aar *AccountingAcessorRepository) FetchPrefeituraCredential(ctx context.Context, cnpj string) (models.Credential, error) {
	var acessorDto dto.CredentialAcessor
	credentialDto, err := aar.store.FetchPrefeituraCredential(ctx, cnpj)

	acessorDto.Login = credentialDto.Login
	acessorDto.Password = credentialDto.Password
	acessorDto.CredentialType = int(credentialDto.CredentialType)

	if err != nil {
		return models.Credential{}, err
	}

	return aar.converter.FromCredentialDtoToModel(acessorDto), nil
}

func (aar *AccountingAcessorRepository) FetchNfeByCnpj(ctx context.Context, cnpj string, competence time.Time) ([]models.Nfse, error) {
	var nfseAcessorList []dto.NfseAcessor
	nfses, err := aar.store.FetchNfeByCnpj(ctx, cnpj, competence)
	if err != nil {
		return nil, err
	}
	for _, nfse := range nfses {
		nfseAcessor := dto.NfseAcessor{
			Num:                       nfse.Num,
			Competence:                nfse.Competence,
			Amount:                    nfse.Amount,
			IsPisRetido:               nfse.IsPisRetido,
			IsServicoPrestadoExterior: nfse.IsServicoPrestadoExterior,
			BaseCalculo:               nfse.BaseCalculo,
			RetainedAmount:            nfse.RetainedAmount,
			IssuedAt:                  nfse.IssuedAt,
			TemIssRetido:              false,
			ValorDeducoes:             0,
			Status:                    "",
			TomadorNome:               "",
			ValorServico:              nfse.Amount,
			CnaeCode:                  "",
			AliquotaIss:               0,
			ValorIss:                  0,
			ValorIr:                   0,
			ValorPis:                  0,
			ValorCofins:               0,
			ValorCsll:                 0,
			ValorInss:                 0,
			Id:                        "",
			Anexo:                     "",
		}
		nfseAcessorList = append(nfseAcessorList, nfseAcessor)
	}

	return aar.converter.FromNfseAcessorListDtoToModel(nfseAcessorList), nil
}

func (aar *AccountingAcessorRepository) FetchCompanyCertificate(ctx context.Context, cnpj string) (models.Certificate, error) {
	certificateDto, err := aar.store.FetchCertificate(ctx, cnpj)
	if err != nil {
		return models.Certificate{}, err
	}
	return aar.converter.FromStringToCertificateModel(certificateDto), nil
}

func (aar *AccountingAcessorRepository) FetchArquivoFiscalEmpresa(ctx context.Context, acessorParams accounting_acessor.ArquivoFiscalParams) (models.ArquivoFiscalContent, error) {
	response, err := aar.store.FetchArquivoFiscalEmpresaByTipoDeclaracao(ctx, acessorParams)
	if err != nil {
		arquivoFiscalError := models.ArquivoFiscalError{
			Error: err,
		}
		return models.ArquivoFiscalContent{}, arquivoFiscalError.GetUserError()
	}
	return aar.converter.FromArquivoFiscalAcessorToModel(response), nil
}

func (aar *AccountingAcessorRepository) GetDeclaracaoIsAutomatica(ctx context.Context, cnpj, declaracaoSlugname, competencia string) (bool, error) {
	return aar.store.GetDeclaracaoIsAutomatica(ctx, cnpj, declaracaoSlugname, competencia)
}

func (aar *AccountingAcessorRepository) CriarImpostoMit(ctx context.Context, cnpj, competence string) error {
	return aar.store.CriarImpostoMit(ctx, cnpj, competence)
}

func (aar *AccountingAcessorRepository) CompleteMit(ctx context.Context, cnpj, competence string) error {
	return aar.store.CompleteMit(ctx, cnpj, competence)
}

func (aar *AccountingAcessorRepository) FetchImpostosAPagar(ctx context.Context, cnpj, competence string) (models.ImpostosAPagar, error) {
	response, err := aar.store.FetchImpostosAPagar(ctx, cnpj, competence)
	if err != nil {
		return models.ImpostosAPagar{}, err
	}

	return aar.converter.FromImpostosAPagarResponseToModel(response), nil
}
