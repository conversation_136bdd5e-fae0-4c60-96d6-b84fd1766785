package dto

import (
	"time"
)

type DeclaracaoEmpresaTable struct {
	ID                string    `db:"id"`
	DeclaracaoID      string    `db:"declaracao_id"`
	TenantID          string    `db:"tenant_id"`
	CompanyID         string    `db:"company_id"`
	Competence        time.Time `db:"competence"`
	ClosingDate       string    `db:"closing_date"`
	Slugname          string    `db:"slugname"`
	ProcessInstanceID string    `db:"process_instance_id"`
	CreatedAt         time.Time `db:"created_at"`
	UpdatedAt         time.Time `db:"updated_at"`
}

type DeclaracaoTable struct {
	ID          string    `db:"id"`
	Description string    `db:"description"`
	Slugname    string    `db:"slugname"`
	DeadLineDay int       `db:"deadline_day"`
	CreatedAt   time.Time `db:"created_at"`
	UpdatedAt   time.Time `db:"updated_at"`
}

type PartiesTable struct {
	ID       string `db:"id"`
	TenantID string `db:"tenant_id"`
	Cnpj     string `db:"cnpj"`
}

type DeclaracaoStatusReportTable struct {
	ID                 string     `db:"id"`
	DeclaracaoId       string     `db:"declaracao_id"`
	IbgeCode           string     `db:"city_code"`
	Status             string     `db:"status"`
	ProximaTentativaEm *time.Time `db:"proxima_tentativa_em"`
	UltimaTentativaEm  *time.Time `db:"ultima_tentativa_em"`
}
