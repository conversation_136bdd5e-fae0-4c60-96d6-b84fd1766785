package dto

type CredentialAcessor struct {
	Login          string
	Password       string
	CredentialType int
}

type NfseAcessor struct {
	Num                       int64
	Competence                string
	Amount                    int64
	IsPisRetido               bool
	IsServicoPrestadoExterior bool
	BaseCalculo               int64
	RetainedAmount            int64
	TemIssRetido              bool
	ValorDeducoes             int64
	IssuedAt                  string
	Status                    string
	TomadorNome               string
	ValorServico              int64
	CnaeCode                  string
	AliquotaIss               int64
	ValorIss                  int64
	ValorIr                   int64
	ValorPis                  int64
	ValorCofins               int64
	ValorCsll                 int64
	ValorInss                 int64
	Id                        string
	Anexo                     string
}
