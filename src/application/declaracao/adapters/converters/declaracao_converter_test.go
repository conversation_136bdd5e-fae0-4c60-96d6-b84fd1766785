package converters_test

import (
	"agilize/src/application/declaracao/adapters/converters"
	"agilize/src/application/declaracao/adapters/dto"
	"agilize/src/application/declaracao/src/domain/models"
	"agilize/src/shared/cryptus/uuid"
	"agilize/src/shared/testify"
	"agilize/src/shared/testify/assert"
	"testing"
	"time"

	"go.uber.org/mock/gomock"
)

func TestDeclaracaoConverter(ts *testing.T) {
	ctrl := gomock.NewController(ts)
	defer ctrl.Finish()
	converter := converters.NewDeclaracaoConverter()
	testify.It(ts, "Deve transformar DeclaracaoEmpresa em DeclaracaoEmpresaTable", func(tt *testing.T) {
		declaracaoEmpresa := models.DeclaracaoEmpresa{
			DeclaracaoID:      uuid.NewV4(),
			ProcessInstanceID: uuid.NewV4(),
			CompanyID:         uuid.NewV4(),
			Competence:        time.Now(),
		}

		table, err := converter.FromModelToTable(declaracaoEmpresa)
		assert.Nil(tt, err)
		assert.NotNil(tt, table.ID)
		assert.NotNil(tt, table.DeclaracaoID)
		assert.NotNil(tt, table.ProcessInstanceID)
		assert.NotNil(tt, table.Competence)
		assert.NotNil(tt, table.Competence)
		assert.NotNil(tt, table.CreatedAt)
		assert.NotNil(tt, table.UpdatedAt)

	})
	testify.It(ts, "Deve retornar um erro caso não exista o identificador da empresa", func(tt *testing.T) {
		declaracaoEmpresa := models.DeclaracaoEmpresa{
			DeclaracaoID: uuid.NewV4(),
			Competence:   time.Now(),
		}

		_, err := converter.FromModelToTable(declaracaoEmpresa)

		assert.NotNil(tt, err)
		assert.Equal(tt, err.Error(), "company id not found - 1706118343")

	})
	testify.It(ts, "Deve transformar FromDeclaracaoTable para DeclaracaoModel", func(tt *testing.T) {
		declaracaoTable := dto.DeclaracaoTable{
			ID:          uuid.NewV4(),
			Description: "Teste de descrição",
			Slugname:    "teste_declaracao",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}

		model := converter.FromDeclaracaoTableToDeclaracaoModel(declaracaoTable)

		assert.NotNil(tt, model.ID)
		assert.Equal(tt, model.Description, declaracaoTable.Description)
		assert.Equal(tt, model.Slugname, declaracaoTable.Slugname)
		assert.Equal(tt, model.ID, declaracaoTable.ID)
	})

	testify.It(ts, "Deve transformar FromDeclaracaoTable para DeclaracaoModel", func(tt *testing.T) {
		declaracaoTable := dto.PartiesTable{
			ID:       uuid.NewV4(),
			TenantID: uuid.NewV4(),
		}

		model := converter.FromTablePartiesToModelCompany(declaracaoTable)

		assert.NotNil(tt, model.ID)
		assert.Equal(tt, model.ID, declaracaoTable.ID)
		assert.Equal(tt, model.TenantID, declaracaoTable.TenantID)
	})
}
