package converters

import (
	"agilize/src/application/declaracao/adapters/dto"
	"agilize/src/application/declaracao/src/domain/models"
)

type DeclaracaoStatusReportConverter struct {
}

func NewDeclaracaoStatusReportConverter() DeclaracaoStatusReportConverter {
	return DeclaracaoStatusReportConverter{}
}

func (dc DeclaracaoStatusReportConverter) FromTableToModel(table dto.DeclaracaoStatusReportTable) models.DeclaracaoStatusReport {
	var declaracaoStatusReport models.DeclaracaoStatusReport

	declaracaoStatusReport.ID = table.ID
	declaracaoStatusReport.Status = table.Status
	declaracaoStatusReport.IbgeCode = table.IbgeCode
	declaracaoStatusReport.ProximaTentativaEm = table.ProximaTentativaEm
	declaracaoStatusReport.UltimaTentativaEm = table.UltimaTentativaEm

	return declaracaoStatusReport
}
