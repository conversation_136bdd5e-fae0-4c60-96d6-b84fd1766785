package converters_test

import (
	"agilize/src/application/declaracao/adapters/converters"
	"agilize/src/application/declaracao/adapters/dto"
	"agilize/src/shared/cryptus/uuid"
	"agilize/src/shared/testify"
	"agilize/src/shared/testify/assert"
	"testing"
	"time"

	"go.uber.org/mock/gomock"
)

func TestDeclaracaoStatusReportConverter(ts *testing.T) {
	ctrl := gomock.NewController(ts)
	defer ctrl.Finish()
	converter := converters.NewDeclaracaoStatusReportConverter()
	testify.It(ts, "Deve transformar DeclaracaoStatusReportTable para DeclaracaoStatusReportModel", func(tt *testing.T) {
		horaAtual := time.Now()
		declaracaoStatusReportTable := dto.DeclaracaoStatusReportTable{
			ID:                 uuid.NewV4(),
			DeclaracaoId:       uuid.NewV4(),
			Status:             "status",
			ProximaTentativaEm: &horaAtual,
			UltimaTentativaEm:  &horaAtual,
		}

		model := converter.FromTableToModel(declaracaoStatusReportTable)

		assert.NotNil(tt, model.ID)
		assert.Equal(tt, model.ID, declaracaoStatusReportTable.ID)
		assert.Equal(tt, model.Status, declaracaoStatusReportTable.Status)
		assert.Equal(tt, model.ProximaTentativaEm, declaracaoStatusReportTable.ProximaTentativaEm)
		assert.Equal(tt, model.UltimaTentativaEm, declaracaoStatusReportTable.UltimaTentativaEm)
	})
}
