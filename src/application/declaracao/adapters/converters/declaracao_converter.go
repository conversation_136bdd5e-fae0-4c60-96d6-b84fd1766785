package converters

import (
	"agilize/src/application/declaracao/adapters/dto"
	"agilize/src/application/declaracao/src/domain/models"
	"agilize/src/shared/cryptus/uuid"
	"fmt"
	"time"
)

type DeclaracaoConverter struct {
}

func NewDeclaracaoConverter() DeclaracaoConverter {
	return DeclaracaoConverter{}
}

func (dc DeclaracaoConverter) FromModelToTable(declaracaoEmpresa models.DeclaracaoEmpresa) (dto.DeclaracaoEmpresaTable, error) {
	var dtoTable dto.DeclaracaoEmpresaTable

	if declaracaoEmpresa.CompanyID == "" {
		return dtoTable, fmt.Errorf("company id not found - 1706118343")
	}

	if declaracaoEmpresa.ProcessInstanceID == "" {
		return dtoTable, fmt.Errorf("instancia de processo not found - 1706118341")
	}

	dtoTable.ID = uuid.NewV4()
	dtoTable.DeclaracaoID = declaracaoEmpresa.DeclaracaoID
	dtoTable.CompanyID = declaracaoEmpresa.CompanyID
	dtoTable.ProcessInstanceID = declaracaoEmpresa.ProcessInstanceID
	dtoTable.Competence = declaracaoEmpresa.Competence
	dtoTable.CreatedAt = time.Now()
	dtoTable.UpdatedAt = time.Now()

	return dtoTable, nil
}

func (dc DeclaracaoConverter) FromDeclaracaoTableToDeclaracaoModel(table dto.DeclaracaoTable) models.Declaracao {
	var declaracao models.Declaracao

	declaracao.ID = table.ID
	declaracao.Slugname = table.Slugname
	declaracao.Description = table.Description
	declaracao.DeadLineDay = table.DeadLineDay
	return declaracao
}

func (dc DeclaracaoConverter) FromDeclaracaoEmpresaTableToDeclaracaoEmpresaModel(table dto.DeclaracaoEmpresaTable) models.DeclaracaoEmpresa {
	var declaracao models.DeclaracaoEmpresa

	declaracao.ID = table.ID
	declaracao.DeclaracaoID = table.DeclaracaoID
	declaracao.TenantID = table.TenantID
	declaracao.Competence = table.Competence
	declaracao.CompanyID = table.CompanyID
	declaracao.DeclaracaoSlugname = table.Slugname

	return declaracao
}

func (dc DeclaracaoConverter) FromTablePartiesToModelCompany(table dto.PartiesTable) models.Company {
	var company models.Company

	company.ID = table.ID
	company.TenantID = table.TenantID
	company.Cnpj = table.Cnpj
	return company
}
