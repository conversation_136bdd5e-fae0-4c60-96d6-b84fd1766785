package converters

import (
	"agilize/src/application/declaracao/adapters/dto"
	"agilize/src/application/declaracao/src/domain/models"
	"agilize/src/drivers/datastores/accounting_acessor"
)

type AccountingAcessorConverter struct {
}

func NewAccountingAcessorConverter() AccountingAcessorConverter {
	return AccountingAcessorConverter{}
}

func (pic AccountingAcessorConverter) FromCredentialDtoToModel(credentialDto dto.CredentialAcessor) models.Credential {
	var credential models.Credential
	credential.Login = credentialDto.Login
	credential.Password = credentialDto.Password
	credential.CredentialType = int(credentialDto.CredentialType)

	return credential
}

func (pic AccountingAcessorConverter) FromNfseAcessorListDtoToModel(list []dto.NfseAcessor) []models.Nfse {
	var modelList []models.Nfse
	for _, acessor := range list {
		nfse := models.Nfse{
			Num:                       acessor.Num,
			Competence:                acessor.Competence,
			Amount:                    acessor.Amount,
			IsPisRetido:               acessor.IsPisRetido,
			IsServicoPrestadoExterior: acessor.IsServicoPrestadoExterior,
			BaseCalculo:               acessor.BaseCalculo,
			RetainedAmount:            acessor.RetainedAmount,
			TemIssRetido:              acessor.TemIssRetido,
			ValorDeducoes:             acessor.ValorDeducoes,
			IssuedAt:                  acessor.IssuedAt,
			Status:                    acessor.Status,
			TomadorNome:               acessor.TomadorNome,
			ValorServico:              acessor.ValorServico,
			CnaeCode:                  acessor.CnaeCode,
			AliquotaIss:               acessor.AliquotaIss,
			ValorIss:                  acessor.ValorIss,
			ValorIr:                   acessor.ValorIr,
			ValorPis:                  acessor.ValorPis,
			ValorCofins:               acessor.ValorCofins,
			ValorCsll:                 acessor.ValorCsll,
			ValorInss:                 acessor.ValorInss,
			Id:                        acessor.Id,
			Anexo:                     acessor.Anexo,
		}

		modelList = append(modelList, nfse)
	}
	return modelList
}

func (pic AccountingAcessorConverter) FromStringToCertificateModel(certificateDto accounting_acessor.Certificate) models.Certificate {
	var Certificate models.Certificate
	Certificate.CertificateContent = certificateDto.CertificateContent
	Certificate.Password = certificateDto.Password

	return Certificate
}
func (pic AccountingAcessorConverter) FromArquivoFiscalAcessorToModel(response accounting_acessor.ArquivoFiscalAcessor) models.ArquivoFiscalContent {
	return models.ArquivoFiscalContent{
		Content:   response.Content,
		Extension: response.Extension,
	}
}

func (pic AccountingAcessorConverter) FromImpostosAPagarResponseToModel(response accounting_acessor.ImpostosAPagarResponse) models.ImpostosAPagar {
	var impostos []models.Imposto

	for _, imposto := range response.Data.Impostos {
		impostos = append(impostos, models.Imposto{
			Nome:         imposto.Nome,
			Valor:        imposto.Valor,
			IsTrimestral: imposto.IsTrimestral,
		})
	}

	return models.ImpostosAPagar{
		Impostos: impostos,
	}
}
