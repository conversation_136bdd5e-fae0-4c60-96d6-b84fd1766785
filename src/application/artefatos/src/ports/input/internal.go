package input

import (
	"agilize/src/application/artefatos/src/domain/models"
	"context"
	"os"
)

type ArtefatoService interface {
	Create(ctx context.Context, artefato models.Artefato) (models.Artefato, error)
	CreateArtefatoDeclaracao(ctx context.Context, declaracaoEmpresaId string, artefato models.Artefato) (models.Artefato, error)
	DownloadFile(ctx context.Context, artefato models.Artefato) (models.Artefato, error)
	GetArtefatoDeclaracaoEmpresa(ctx context.Context, declaracaoEmpresaId string, tipoArtefatoSlugname models.TipoArtefatoSlugname) ([]models.Artefato, error)
	GetPreSignedUrlFromArtifact(ctx context.Context, artifact *models.FileDownloaded) (string, error)
	GetFileFromUrl(url string) (*os.File, error)
	CreateArtefatosImpostoEmpresa(ctx context.Context, impostoEmpresaId string, artefatos []models.Artefato) ([]models.Artefato, error)
	RemoveArtefatoImpostoEmpresa(ctx context.Context, impostoEmpresaId string, artifactId string) error
	GetAllArtefatosImpostoEmpresa(ctx context.Context, impostoEmpresaId string) ([]models.Artefato, error)
}

type FileUpload interface {
	GetTenant() string
	GetCnpj() string
	GetFileName() string
	GetFileToUpload() []byte
}

type FileDownloaded interface {
	GetTenant() string
	GetCnpj() string
	GetFileName() string
	GetFileDownloaded() []byte
}
