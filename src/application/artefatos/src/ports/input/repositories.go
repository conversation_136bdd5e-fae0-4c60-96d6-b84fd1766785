package input

import (
	"agilize/src/application/artefatos/src/domain/models"
	"context"
	"time"
)

type Storage interface {
	UploadFile(ctx context.Context, file FileUpload) error
	DownloadFile(ctx context.Context, file FileDownloaded) ([]byte, error)
	PreSignedUrl(ctx context.Context, file FileDownloaded) (string, error)
	GetFullFilePath() string
	GetFileName() string
}

type ArtefatoRepository interface {
	Create(context.Context, models.Artefato) (models.Artefato, error)
	CreateArtefatoDeclaracao(ctx context.Context, declaracaoId string, item models.Artefato) error
	FindArtefatoDeclaracaoEmpresa(ctx context.Context, declaracaoEmpresaId string, slugname models.TipoArtefatoSlugname) ([]models.Artefato, error)
	FindAllArtefatoDeclaracaoEmpresa(ctx context.Context, declaracaoEmpresaID string) ([]models.Artefato, error)
	RemoveArtefato(ctx context.Context, artefatoID string) error
	DeleteArtefatoLogicamente(ctx context.Context, artefatoID string) error
	FindArtefato(ctx context.Context, artefatoID string) (models.Artefato, error)
	FindArtifactsByType(ctx context.Context, artifactTypeSlugname string, competence time.Time) ([]models.ArtefatoByType, error)
	FindEFDICMSIPIArtifacts(ctx context.Context, competence time.Time) ([]models.ArtefatoByType, error)
	FindSPEDArtifacts(ctx context.Context, competence time.Time) ([]models.ArtefatoByType, error)
	FindDCTFArtifacts(ctx context.Context, competence time.Time) ([]models.ArtefatoByType, error)
	CreateArtefatosImpostoEmpresa(ctx context.Context, impostoEmpresaId string, items []models.Artefato) error
	FindAllArtefatosImpostoEmpresa(ctx context.Context, impostoEmpresaId string) ([]models.Artefato, error)
	RemoveArtefatoImpostoEmpresa(ctx context.Context, impostoEmpresaId string, artifactId string) error
	FindGuiaByImpostoEmpresa(ctx context.Context, impostoEmpresaId string) (models.Artefato, error)
}

type TipoArtefatoRepository interface {
	GetBySlugname(ctx context.Context, slugname models.TipoArtefatoSlugname) (models.TipoArtefato, error)
	FindTipoArtefatoByProcess(ctx context.Context, processID string) ([]models.TipoArtefato, error)
	FindTipoArtefatoByProcessKey(ctx context.Context, processKey string) ([]models.TipoArtefato, error)
}
