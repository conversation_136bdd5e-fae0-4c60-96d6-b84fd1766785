package dto

type EmpresaTable struct {
	ID                 string `db:"id"`
	TenantID           string `db:"tenant_id"`
	CNPJ               string `db:"cnpj"`
	Name               string `db:"name"`
	CityCode           string `db:"city_code"`
	InscricaoMunicipal string `db:"inscricao_municipal"`
	RegimeTributario   string `db:"regime_tributario"`
	ActivityType       int    `db:"activity_type"`
	NomeFantasia       string `db:"nome_fantasia"`
	Cep                string `db:"cep"`
}
