package output

import "time"

type RenovacaoCertificadoHttpResponse struct {
	Id                     string              `json:"id"`
	ProcessInstanceId      string              `json:"process_instance_id"`
	Empresa                EmpresaHttpResponse `json:"empresa"`
	IsGratuito             bool                `json:"is_gratuito"`
	IsInadimplente         bool                `json:"is_inadimplente"`
	DataVencimento         time.Time           `json:"data_vencimento"`
	Voucher                string              `json:"voucher"`
	Identifier             int64               `json:"identifier"`
	Status                 int64               `json:"status"`
	ExibirLinkEmissao      bool                `json:"exibir_link_emissao"`
	FaturaPersonalizadaId  string              `json:"fatura_personalizada_id"`
	FaturaPersonalizadaURI string              `json:"fatura_personalizada_uri"`
}
