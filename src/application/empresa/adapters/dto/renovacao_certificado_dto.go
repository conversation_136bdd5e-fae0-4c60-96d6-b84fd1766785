package dto

import (
	"database/sql"
	"time"
)

type RenovacaoCertificadoTable struct {
	ID                     string         `db:"id"`
	Cnpj                   string         `db:"cnpj"`
	Status                 int            `db:"status"`
	IsGratuito             bool           `db:"is_gratuito"`
	IsInadimplente         bool           `db:"is_inadimplente"`
	DataVencimento         time.Time      `db:"data_vencimento"`
	Voucher                sql.NullString `db:"voucher"`
	Identifier             sql.NullInt64  `db:"identitifer"`
	LastNofiedAt           sql.NullTime   `db:"last_nofied_at"`
	ProcessInstanceId      string         `db:"process_instance_id"`
	FaturaPersonalizadaId  sql.NullString `db:"fatura_personalizada_id"`
	FaturaPersonalizadaUri sql.NullString `db:"fatura_personalizada_uri"`
	ExibirLinkEmissao      sql.NullBool   `db:"exibir_link_emissao"`
}

type RenovacaoCertificadoDto struct {
	Cnpj              string
	ProcessInstanceId string
	DataVencimento    string
	IsGratuito        bool
	IsInadimplente    bool
}

type CNPJParam struct {
	Cnpj string `json:"cnpj" query:"cnpj" param:"cnpj" form:"cnpj" valid:"type(string)"`
}
