package repositories

import (
	"agilize/src/application/empresa/adapters/converters"
	"agilize/src/application/empresa/adapters/dto"
	"agilize/src/application/empresa/src/domain/models"
	"agilize/src/application/empresa/src/ports/input"
	"agilize/src/drivers/datastores/db"
	errorhandler "agilize/src/shared/errors"
	"context"
	"strings"
)

type EmpresaRepository struct {
	sqlDataStore db.SqlDataStore
	converter    converters.EmpresaConverter
}

func NewCompanyRepository(sqlDataStore db.SqlDataStore) input.EmpresaRepository {
	return &EmpresaRepository{
		sqlDataStore: sqlDataStore,
		converter:    converters.NewEmpresaConverter(),
	}
}

func (er *EmpresaRepository) FindAll(ctx context.Context) ([]models.Empresa, error) {
	var item []dto.EmpresaTable

	err := er.sqlDataStore.WithContext(ctx).QueryAll(findAllEmpresas, &item)

	if err != nil && strings.Contains(err.Error(), "no rows in result set") {
		return []models.Empresa{}, err
	}

	if err != nil {
		return []models.Empresa{}, errorhandler.Error(err, "1711047955")
	}

	return er.converter.FromTableListToModelList(item), nil

}

func (er *EmpresaRepository) FindOneById(ctx context.Context, empresaId string) (models.Empresa, error) {
	var item dto.EmpresaTable

	err := er.sqlDataStore.WithContext(ctx).QueryOne(findEmpresa, &item, empresaId)

	if err != nil && strings.Contains(err.Error(), "no rows in result set") {
		return models.Empresa{}, err
	}

	if err != nil {
		return models.Empresa{}, err
	}

	return er.converter.FromTableToModel(item), nil
}

func (er *EmpresaRepository) FindOneByCnpj(ctx context.Context, cnpj string) (models.Empresa, error) {
	var item dto.EmpresaTable

	err := er.sqlDataStore.WithContext(ctx).QueryOne(findEmpresaByCnpj, &item, cnpj)

	if err != nil && strings.Contains(err.Error(), "no rows in result set") {
		return models.Empresa{}, nil
	}

	if err != nil {
		return models.Empresa{}, err
	}

	return er.converter.FromTableToModel(item), nil
}
