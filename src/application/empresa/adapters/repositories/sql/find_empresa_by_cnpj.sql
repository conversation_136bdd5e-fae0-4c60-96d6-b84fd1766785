SELECT p.id,
       p.tenant_id,
       COALESCE(p.cnpj, '')                 AS cnpj,
       COALESCE(p.name, '')                 AS name,
       COALESCE(ci.city_code, '')           AS city_code,
       COALESCE(ci.inscricao_municipal, '') AS inscricao_municipal,
       COALESCE(ci.regime_tributario, '')   AS regime_tributario,
       COALESCE(ci.activity_type, 0)        AS activity_type,
       COALESCE(p.nome_fantasia, '')        AS nome_fantasia,
       COALESCE(e.cep, '')                  AS cep
FROM parties AS p
         JOIN company_informations AS ci ON ci.id = p.id
         LEFT JOIN enderecos AS e ON e.company_id = ci.id
WHERE p.cnpj = $1
