SELECT p.id,
       p.tenant_id,
       COALESCE(p.cnpj, '')                 AS cnpj,
       COALESCE(p.name, '')                 AS name,
       COALESCE(ci.city_code, '')           AS city_code,
       COALESCE(ci.inscricao_municipal, '') AS inscricao_municipal,
       COALESCE(ci.regime_tributario, '')   AS regime_tributario,
       COALESCE(ci.activity_type, '')       AS activity_type
FROM parties AS p
         JOIN company_informations AS ci ON ci.id = p.id
WHERE p.id = $1
