SELECT rc.id,
       p.cnpj,
       rc.is_gratuito,
       rc.is_inadimplente,
       rc.data_vencimento,
       rc.voucher,
       rc.identitifer,
       rc.status,
       rc.process_instance_id,
       rc.fatura_personalizada_id,
       rc.fatura_personalizada_uri,
       rc.exibir_link_emissao
FROM renovacao_certificados rc
         JOIN public.parties p ON rc.company_id = p.id
WHERE p.cnpj = $1
  AND status != $2
ORDER BY created_at DESC LIMIT 1;
