package repositories

import (
	"agilize/src/application/empresa/adapters/converters"
	"agilize/src/application/empresa/adapters/dto"
	"agilize/src/application/empresa/src/domain/models"
	"agilize/src/application/empresa/src/ports/input"
	"agilize/src/drivers/datastores/db"
	errorhandler "agilize/src/shared/errors"
	"context"
	"strings"
	"time"
)

type CertificadoRepository struct {
	store     db.SqlDataStore
	converter converters.CertificadoConverter
}

func NewCertificadoRepository(sqlDataStore db.SqlDataStore) input.CertificadoRepository {
	return &CertificadoRepository{
		store:     sqlDataStore,
		converter: converters.NewCertificadoConverter(),
	}
}

func (cr *CertificadoRepository) FindOneValidRenovacaoCertificadoByCnpj(ctx context.Context, cnpj string) (models.RenovacaoCertificado, error) {
	var item dto.RenovacaoCertificadoTable

	err := cr.store.WithContext(ctx).QueryOne(findOneRenovacaoCertificado, &item, cnpj, models.FINALIZADO)

	if err != nil && strings.Contains(err.Error(), "no rows in result set") {
		return models.RenovacaoCertificado{}, nil
	}

	if err != nil {
		return models.RenovacaoCertificado{}, errorhandler.Error(err, "1725569299")
	}

	return cr.converter.FromTableToModel(item), nil
}

func (cr *CertificadoRepository) FindOneRenovacaoCertificadoById(ctx context.Context, id string) (models.RenovacaoCertificado, error) {
	var item dto.RenovacaoCertificadoTable

	err := cr.store.WithContext(ctx).QueryOne(findOneRenovacaoCertificadoById, &item, id)

	if err != nil && strings.Contains(err.Error(), "no rows in result set") {
		return models.RenovacaoCertificado{}, nil
	}

	if err != nil {
		return models.RenovacaoCertificado{}, errorhandler.Error(err, "1726083135")
	}

	return cr.converter.FromTableToModel(item), nil
}

func (cr *CertificadoRepository) CreateRenovacaoCertificado(ctx context.Context, item models.RenovacaoCertificado) (models.RenovacaoCertificado, error) {
	q := "INSERT INTO renovacao_certificados(id, company_id, process_instance_id, status, is_gratuito, is_inadimplente, data_vencimento, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6,$7, $8, $8)"
	err := cr.store.WithContext(ctx).Exec(
		q,
		item.Id,
		item.Empresa.ID,
		item.ProcessInstanceId,
		item.Status,
		item.IsGratuito,
		item.IsInadimplente,
		item.DataVencimento,
		time.Now(),
	)
	return item, err
}

func (cr *CertificadoRepository) UpdateRenovacaoCertificado(ctx context.Context, rc models.RenovacaoCertificado) (models.RenovacaoCertificado, error) {
	q := "UPDATE renovacao_certificados SET voucher=$1,identitifer=$2, is_gratuito=$4, is_inadimplente=$5, status=$6, updated_at=$7, fatura_personalizada_id=$8, fatura_personalizada_uri=$9, exibir_link_emissao=$10 WHERE id = $3"
	err := cr.store.WithContext(ctx).Exec(
		q,
		rc.Voucher,
		rc.Identifier,
		rc.Id,
		rc.IsGratuito,
		rc.IsInadimplente,
		rc.Status,
		time.Now(),
		rc.FaturaPersolanizadaId,
		rc.FaturaPersolanizadaURI,
		rc.ExibirLinkEmissao,
	)
	return rc, err
}

func (cr *CertificadoRepository) ListAll(ctx context.Context) ([]models.RenovacaoCertificado, error) {
	var items []dto.RenovacaoCertificadoTable

	q := "SELECT rc.id, p.cnpj, rc.process_instance_id, rc.status, rc.is_gratuito, rc.is_inadimplente, rc.data_vencimento, rc.voucher, rc.identitifer, rc.fatura_personalizada_id, rc.fatura_personalizada_uri, rc.exibir_link_emissao FROM renovacao_certificados rc INNER JOIN parties p ON p.id = rc.company_id WHERE rc.status <> $1"
	err := cr.store.WithContext(ctx).QueryAll(q, &items, models.FINALIZADO)

	if err != nil && strings.Contains(err.Error(), "no rows in result set") {
		return []models.RenovacaoCertificado{}, nil
	}

	if err != nil {
		return nil, errorhandler.Error(err, "1726083136")
	}

	var result []models.RenovacaoCertificado
	for _, item := range items {
		result = append(result, cr.converter.FromTableToModel(item))
	}

	return result, nil
}

func (cr *CertificadoRepository) FindRenovacoesWithoutExibirLinkEmissao(ctx context.Context) ([]models.RenovacaoCertificado, error) {
	var items []dto.RenovacaoCertificadoTable

	q := "SELECT rc.id, p.cnpj, rc.process_instance_id, rc.status, rc.is_gratuito, rc.is_inadimplente, rc.data_vencimento, rc.voucher, rc.identitifer, rc.fatura_personalizada_id, rc.fatura_personalizada_uri, rc.exibir_link_emissao FROM renovacao_certificados rc INNER JOIN parties p ON p.id = rc.company_id WHERE rc.voucher <> '' AND rc.is_gratuito = false AND rc.exibir_link_emissao = false AND rc.status <> 3"
	err := cr.store.WithContext(ctx).QueryAll(q, &items)

	if err != nil && strings.Contains(err.Error(), "no rows in result set") {
		return []models.RenovacaoCertificado{}, nil
	}

	if err != nil {
		return nil, errorhandler.Error(err, "1726083136")
	}

	var result []models.RenovacaoCertificado
	for _, item := range items {
		result = append(result, cr.converter.FromTableToModel(item))
	}

	return result, nil
}
