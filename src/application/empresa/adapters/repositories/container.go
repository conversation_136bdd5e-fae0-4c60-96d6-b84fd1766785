package repositories

import (
	"agilize/src/application/empresa/src/ports/input"
	"agilize/src/drivers/datastores"
	"sync"
)

var once sync.Once
var empresaRepository input.EmpresaRepository
var acessorRepository input.AcessorRepository
var certificadoRepository input.CertificadoRepository

type EmpresaRepositoryContainer interface {
	EmpresaRepository() input.EmpresaRepository
	AcessorRepository() input.AcessorRepository
	CertificadoRepository() input.CertificadoRepository
}

type container struct {
	empresaRepository     interface{}
	acessorRepository     interface{}
	certificadoRepository interface{}
}

func NewEmpresaRepositoryContainer(ds datastores.DataStore) EmpresaRepositoryContainer {
	once.Do(func() {
		empresaRepository = NewCompanyRepository(ds.<PERSON>g<PERSON>())
		acessorRepository = NewAcessorRepository(ds.Accounting())
		certificadoRepository = NewCertificadoRepository(ds.Postgres())
	})
	return &container{
		empresaRepository:     empresaRepository,
		acessorRepository:     acessorRepository,
		certificadoRepository: certificadoRepository,
	}
}

func (c *container) EmpresaRepository() input.EmpresaRepository {
	return empresaRepository
}

func (c *container) AcessorRepository() input.AcessorRepository {
	return acessorRepository
}

func (c *container) CertificadoRepository() input.CertificadoRepository {
	return certificadoRepository
}
