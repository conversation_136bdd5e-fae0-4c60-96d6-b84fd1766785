package repositories

import (
	declaracaoModels "agilize/src/application/declaracao/src/domain/models"
	"agilize/src/application/empresa/adapters/converters"
	"agilize/src/application/empresa/adapters/dto"
	"agilize/src/application/empresa/src/domain/models"
	empresaInput "agilize/src/application/empresa/src/ports/input"
	"agilize/src/drivers/datastores/accounting_acessor"
	"context"
	"strings"
)

type AcessorRepository struct {
	accountingStore accounting_acessor.AccountingStore
	converter       converters.AcessorConverter
}

func NewAcessorRepository(store accounting_acessor.AccountingStore) empresaInput.AcessorRepository {
	return &AcessorRepository{
		accountingStore: store,
		converter:       converters.NewAcessorConverter(),
	}
}

func (ar *AcessorRepository) FetchRenovacaoCertificadoList(ctx context.Context) ([]dto.RenovacaoCertificadoDto, error) {
	credentials, err := ar.accountingStore.FetchRenovacaoCertificadoList(ctx)
	if err != nil {
		return []dto.RenovacaoCertificadoDto{}, err
	}
	return ar.converter.FromRenovacaoCertificadoResponseListToRenovacaoCertificadoModel(credentials), nil
}

func (ar *AcessorRepository) FetchRenovacaoCertificadoSubiuNovoCertificado(ctx context.Context, cnpj string) (bool, error) {
	response, err := ar.accountingStore.FetchRenovacaoCertificadoSubiuNovo(ctx, cnpj)
	if err != nil {
		return false, err
	}
	return response.Data.TemNovoCertificado, nil
}

func (ar *AcessorRepository) CompleteMit(ctx context.Context, cnpj, competence string) error {
	return ar.accountingStore.CompleteMit(ctx, cnpj, competence)
}

func (ar *AcessorRepository) FetchImpostosAPagar(ctx context.Context, cnpj, competence string) (declaracaoModels.ImpostosAPagar, error) {
	response, err := ar.accountingStore.FetchImpostosAPagar(ctx, cnpj, competence)
	if err != nil {
		return declaracaoModels.ImpostosAPagar{}, err
	}

	return ar.converter.FromImpostosAPagarResponseToModel(response), nil
}

func (ar *AcessorRepository) FetchCobrancaFoiPaga(ctx context.Context, faturaId string) (bool, error) {
	response, err := ar.accountingStore.FetchCobrancaFoiPaga(ctx, faturaId)
	if err != nil && strings.Contains(err.Error(), "no rows in result set") {
		return false, nil
	}
	if err != nil {
		return false, err
	}

	return response.Data.CobrancaFoiPaga, nil
}

func (ar *AcessorRepository) CreateCobranca(ctx context.Context, params accounting_acessor.CreateCobrancaParams) (models.Cobranca, error) {
	response, err := ar.accountingStore.CreateCobranca(ctx, params)
	if err != nil {
		return models.Cobranca{}, err
	}
	return ar.converter.FromCobrancaResponseToCobranca(response)
}

func (ar *AcessorRepository) FetchRenovacaoCertificado(ctx context.Context, cnpj string) (dto.RenovacaoCertificadoDto, error) {
	response, err := ar.accountingStore.FetchRenovacaoCertificado(ctx, cnpj)
	if err != nil {
		return dto.RenovacaoCertificadoDto{}, err
	}
	return ar.converter.FromRenovacaoCertificadoResponseToRenovacaoCertificadoDto(response), nil
}

func (ar *AcessorRepository) FetchIsExCliente(ctx context.Context, cnpj string) (bool, error) {
	response, err := ar.accountingStore.FetchIsExCliente(ctx, cnpj)
	if err != nil {
		return false, err
	}
	return response.Data.IsExCliente, nil
}

func (ar *AcessorRepository) FetchCompanyInfo(ctx context.Context, cnpj string) (accounting_acessor.CompanyInfo, error) {
	companyInfo, err := ar.accountingStore.FetchCompanyInfo(ctx, cnpj)
	if err != nil {
		return accounting_acessor.CompanyInfo{}, err
	}

	return companyInfo, nil
}
