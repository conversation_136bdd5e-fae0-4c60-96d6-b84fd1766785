package converters

import (
	"agilize/src/drivers/datastores/accounting_acessor"
	"agilize/src/shared/testify"
	"agilize/src/shared/testify/assert"
	"testing"
)

func TestFromRenovacaoCertificadoResponseToRenovacaoCertificadoModel(ts *testing.T) {
	testify.It(ts, "Convert credential table to model", func(t *testing.T) {
		in := accounting_acessor.RenovacaoCertificadoResponse{
			Cnpj:           "************",
			DataVencimento: "2024-09-04",
			IsGratuito:     false,
			IsInadimplente: false,
		}
		out := NewAcessorConverter().FromRenovacaoCertificadoResponseToRenovacaoCertificadoModel(in)
		assert.Equal(t, out.Cnpj, "************")
		assert.Equal(t, out.DataVencimento, "2024-09-04")
		assert.Equal(t, out.IsGratuito, false)
		assert.Equal(t, out.IsInadimplente, false)
	})
}
