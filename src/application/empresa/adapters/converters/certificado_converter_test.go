package converters

import (
	"agilize/src/application/empresa/adapters/dto"
	"agilize/src/application/empresa/adapters/dto/output"
	"agilize/src/application/empresa/src/domain/models"
	"agilize/src/shared/testify"
	"agilize/src/shared/testify/assert"
	"database/sql"
	"testing"
	"time"
)

func TestFromTableToModel(ts *testing.T) {
	testify.It(ts, "Convert default", func(t *testing.T) {
		in := dto.RenovacaoCertificadoTable{
			ID:             "b42d3152-4a91-45c9-92d4-bf6d1ec7b691",
			Cnpj:           "1111111111111111111",
			IsGratuito:     false,
			IsInadimplente: true,
			Status:         1,
			DataVencimento: time.Date(2024, 9, 6, 0, 0, 0, 0, time.UTC),
		}
		out := NewCertificadoConverter().FromTableToModel(in)
		assert.Equal(t, out.Id, "b42d3152-4a91-45c9-92d4-bf6d1ec7b691")
		assert.Equal(t, out.Empresa.CNPJ, "1111111111111111111")
		assert.Equal(t, out.IsGratuito, false)
		assert.Equal(t, out.IsInadimplente, true)
		assert.Equal(t, out.Status, models.RenovacaoCertificadoStatus(1))
		assert.Equal(t, out.DataVencimento.Format(time.DateOnly), "2024-09-06")
	})

	testify.It(ts, "Convert with fields", func(t *testing.T) {
		in := dto.RenovacaoCertificadoTable{
			ID:             "9e27671c-d504-41d3-9fea-678ab9259bf7",
			Cnpj:           "22222222222222",
			IsGratuito:     true,
			IsInadimplente: false,
			Status:         0,
			DataVencimento: time.Date(2024, 9, 6, 0, 0, 0, 0, time.UTC),
			Voucher: sql.NullString{
				String: "voucherCode",
				Valid:  true,
			},
			Identifier: sql.NullInt64{
				Int64: 123456,
				Valid: true,
			},
		}
		out := NewCertificadoConverter().FromTableToModel(in)
		assert.Equal(t, out.Id, "9e27671c-d504-41d3-9fea-678ab9259bf7")
		assert.Equal(t, out.Empresa.CNPJ, "22222222222222")
		assert.Equal(t, out.IsGratuito, true)
		assert.Equal(t, out.IsInadimplente, false)
		assert.Equal(t, out.DataVencimento.Format(time.DateOnly), "2024-09-06")
		assert.Equal(t, out.Voucher, "voucherCode")
		assert.Equal(t, out.Identifier, int64(123456))
		assert.Equal(t, out.Status, models.RenovacaoCertificadoStatus(0))
	})
}

func TestCertificadoConverter_FromModelToResponse(t *testing.T) {
	converter := NewCertificadoConverter()
	now := time.Now()

	input := models.RenovacaoCertificado{
		Id:                "123",
		ProcessInstanceId: "456",
		Empresa: models.Empresa{
			ID:                 "789",
			TenantID:           "tenant123",
			CNPJ:               "12345678901234",
			Name:               "Test Company",
			CityCode:           "1234567",
			InscricaoMunicipal: "IM123456",
			RegimeTributario:   "Simples Nacional",
			ActivityType:       1,
		},
		IsGratuito:     true,
		IsInadimplente: false,
		DataVencimento: now,
		Voucher:        "VOUCHER123",
		Identifier:     987654,
		Status:         0,
	}

	result := converter.FromModelToResponse(input)

	expected := output.RenovacaoCertificadoHttpResponse{
		Id:                "123",
		ProcessInstanceId: "456",
		Empresa: output.EmpresaHttpResponse{
			ID:                 "789",
			TenantID:           "tenant123",
			CNPJ:               "12345678901234",
			Name:               "Test Company",
			CityCode:           "1234567",
			InscricaoMunicipal: "IM123456",
			RegimeTributario:   "Simples Nacional",
			ActivityType:       1,
		},
		IsGratuito:     true,
		IsInadimplente: false,
		DataVencimento: now,
		Voucher:        "VOUCHER123",
		Identifier:     987654,
		Status:         0,
	}

	assert.Equal(t, expected, result)
}
