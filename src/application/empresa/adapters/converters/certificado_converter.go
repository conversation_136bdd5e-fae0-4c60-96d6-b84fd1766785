package converters

import (
	"agilize/src/application/empresa/adapters/dto"
	"agilize/src/application/empresa/adapters/dto/output"
	"agilize/src/application/empresa/src/domain/models"
)

type CertificadoConverter struct {
}

func NewCertificadoConverter() CertificadoConverter {
	return CertificadoConverter{}
}

func (cc CertificadoConverter) FromTableToModel(input dto.RenovacaoCertificadoTable) models.RenovacaoCertificado {
	renovacaoCertificado := models.RenovacaoCertificado{
		Id: input.ID,
		Empresa: models.Empresa{
			CNPJ: input.Cnpj,
		},
		IsGratuito:        input.IsGratuito,
		IsInadimplente:    input.IsInadimplente,
		DataVencimento:    input.DataVencimento,
		Status:            models.RenovacaoCertificadoStatus(input.Status),
		ProcessInstanceId: input.ProcessInstanceId,
	}

	if input.Identifier.Valid {
		renovacaoCertificado.Identifier = input.Identifier.Int64
	}

	if input.Voucher.Valid {
		renovacaoCertificado.Voucher = input.Voucher.String
	}

	if input.FaturaPersonalizadaId.Valid {
		renovacaoCertificado.FaturaPersolanizadaId = input.FaturaPersonalizadaId.String
	}

	if input.FaturaPersonalizadaUri.Valid {
		renovacaoCertificado.FaturaPersolanizadaURI = input.FaturaPersonalizadaUri.String
	}

	if input.ExibirLinkEmissao.Valid {
		renovacaoCertificado.ExibirLinkEmissao = input.ExibirLinkEmissao.Bool
	}

	return renovacaoCertificado
}

func (cc CertificadoConverter) FromModelToResponse(input models.RenovacaoCertificado) output.RenovacaoCertificadoHttpResponse {
	return output.RenovacaoCertificadoHttpResponse{
		Id:                input.Id,
		ProcessInstanceId: input.ProcessInstanceId,
		Empresa: output.EmpresaHttpResponse{
			ID:                 input.Empresa.ID,
			TenantID:           input.Empresa.TenantID,
			CNPJ:               input.Empresa.CNPJ,
			Name:               input.Empresa.Name,
			CityCode:           input.Empresa.CityCode,
			InscricaoMunicipal: input.Empresa.InscricaoMunicipal,
			RegimeTributario:   input.Empresa.RegimeTributario,
			ActivityType:       input.Empresa.ActivityType,
		},
		IsGratuito:             input.IsGratuito,
		IsInadimplente:         input.IsInadimplente,
		DataVencimento:         input.DataVencimento,
		Voucher:                input.Voucher,
		Identifier:             input.Identifier,
		Status:                 int64(rune(input.Status)),
		ExibirLinkEmissao:      input.ExibirLinkEmissao,
		FaturaPersonalizadaId:  input.FaturaPersolanizadaId,
		FaturaPersonalizadaURI: input.FaturaPersolanizadaURI,
	}
}
