package converters

import (
	declaracaoModels "agilize/src/application/declaracao/src/domain/models"
	"agilize/src/application/empresa/adapters/dto"
	"agilize/src/application/empresa/src/domain/models"
	"agilize/src/drivers/datastores/accounting_acessor"
	"time"
)

const (
	timeFormat = "2006-01-02T15:04:05-0700"
)

type AcessorConverter struct {
}

func NewAcessorConverter() AcessorConverter {
	return AcessorConverter{}
}

func (c AcessorConverter) FromRenovacaoCertificadoResponseListToRenovacaoCertificadoModel(list accounting_acessor.RenovacaoCertificadoResponseList) []dto.RenovacaoCertificadoDto {
	var renovacoesCertificado []dto.RenovacaoCertificadoDto
	for _, renovacaoCertificadoResponse := range list.Data {
		renovacoesCertificado = append(renovacoesCertificado, c.FromRenovacaoCertificadoResponseToRenovacaoCertificadoModel(renovacaoCertificadoResponse))
	}
	return renovacoesCertificado
}

func (c AcessorConverter) FromRenovacaoCertificadoResponseToRenovacaoCertificadoModel(renovacaoCertificadoResponse accounting_acessor.RenovacaoCertificadoResponse) dto.RenovacaoCertificadoDto {
	return dto.RenovacaoCertificadoDto{
		Cnpj:           renovacaoCertificadoResponse.Cnpj,
		DataVencimento: renovacaoCertificadoResponse.DataVencimento,
		IsGratuito:     renovacaoCertificadoResponse.IsGratuito,
		IsInadimplente: renovacaoCertificadoResponse.IsInadimplente,
	}
}

func (ac *AcessorConverter) FromRenovacaoCertificadoResponseToRenovacaoCertificadoDto(response accounting_acessor.RenovacaoCertificadoResponse) dto.RenovacaoCertificadoDto {
	return dto.RenovacaoCertificadoDto{
		Cnpj:           response.Cnpj,
		DataVencimento: response.DataVencimento,
		IsGratuito:     response.IsGratuito,
		IsInadimplente: response.IsInadimplente,
	}
}

func (ac *AcessorConverter) FromCobrancaResponseToCobranca(response accounting_acessor.CobrancaResponse) (models.Cobranca, error) {

	empresa := models.Empresa{
		ID:   response.Company.Identity,
		Name: response.Company.Name,
	}

	fatura := models.FaturaPersonalizada{
		URI:      response.FaturaPersonalizada.URI,
		Identity: response.FaturaPersonalizada.Identity,
	}

	startDate, err := time.Parse(timeFormat, response.StartDate)
	if err != nil {
		return models.Cobranca{}, err
	}

	return models.Cobranca{
		ID:                  response.Identity,
		Company:             empresa,
		Type:                response.Type,
		TotalValue:          response.Value,
		Description:         response.Description,
		Quantity:            response.Quantity,
		InitialPaymentValue: response.Value,
		StartDate:           startDate,
		FaturaPersonalizada: fatura,
	}, nil
}

func (c AcessorConverter) FromImpostosAPagarResponseToModel(response accounting_acessor.ImpostosAPagarResponse) declaracaoModels.ImpostosAPagar {
	var impostos []declaracaoModels.Imposto

	for _, imposto := range response.Data.Impostos {
		impostos = append(impostos, declaracaoModels.Imposto{
			Nome:         imposto.Nome,
			Valor:        imposto.Valor,
			IsTrimestral: imposto.IsTrimestral,
		})
	}

	return declaracaoModels.ImpostosAPagar{
		Impostos: impostos,
	}
}
