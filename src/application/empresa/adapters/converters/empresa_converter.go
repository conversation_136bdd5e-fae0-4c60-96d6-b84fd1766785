package converters

import (
	"agilize/src/application/empresa/adapters/dto"
	"agilize/src/application/empresa/src/domain/models"
)

type EmpresaConverter struct {
}

func NewEmpresaConverter() EmpresaConverter {
	return EmpresaConverter{}
}

func (c EmpresaConverter) FromTableListToModelList(list []dto.EmpresaTable) []models.Empresa {
	var empresas []models.Empresa
	for _, empresa := range list {
		empresas = append(empresas, c.FromTableToModel(empresa))
	}
	return empresas
}

func (c EmpresaConverter) FromTableToModel(empresa dto.EmpresaTable) models.Empresa {
	return models.Empresa{
		ID:                 empresa.ID,
		TenantID:           empresa.TenantID,
		CNPJ:               empresa.CNPJ,
		Name:               empresa.Name,
		CityCode:           empresa.CityCode,
		RegimeTributario:   empresa.RegimeTributario,
		InscricaoMunicipal: empresa.InscricaoMunicipal,
		ActivityType:       empresa.ActivityType,
	}
}
