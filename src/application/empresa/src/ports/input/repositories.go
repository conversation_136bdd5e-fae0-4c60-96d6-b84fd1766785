package input

import (
	declaracaoModels "agilize/src/application/declaracao/src/domain/models"
	"agilize/src/application/empresa/adapters/dto"
	"agilize/src/application/empresa/src/domain/models"
	"agilize/src/drivers/datastores/accounting_acessor"
	"context"
)

type EmpresaRepository interface {
	FindOneById(ctx context.Context, empresaId string) (models.Empresa, error)
	FindOneByCnpj(ctx context.Context, cnpj string) (models.Empresa, error)
	FindAll(ctx context.Context) ([]models.Empresa, error)
}

type CertificadoRepository interface {
	FindOneValidRenovacaoCertificadoByCnpj(ctx context.Context, cnpj string) (models.RenovacaoCertificado, error)
	FindOneRenovacaoCertificadoById(ctx context.Context, id string) (models.RenovacaoCertificado, error)
	CreateRenovacaoCertificado(context.Context, models.RenovacaoCertificado) (models.RenovacaoCertificado, error)
	UpdateRenovacaoCertificado(context.Context, models.RenovacaoCertificado) (models.RenovacaoCertificado, error)
	ListAll(ctx context.Context) ([]models.RenovacaoCertificado, error)
	FindRenovacoesWithoutExibirLinkEmissao(ctx context.Context) ([]models.RenovacaoCertificado, error)
}

type AcessorRepository interface {
	FetchRenovacaoCertificadoList(ctx context.Context) ([]dto.RenovacaoCertificadoDto, error)
	FetchRenovacaoCertificadoSubiuNovoCertificado(ctx context.Context, cnpj string) (bool, error)
	FetchIsExCliente(ctx context.Context, cnpj string) (bool, error)
	FetchCobrancaFoiPaga(ctx context.Context, faturaId string) (bool, error)
	CreateCobranca(ctx context.Context, params accounting_acessor.CreateCobrancaParams) (models.Cobranca, error)
	FetchRenovacaoCertificado(ctx context.Context, cnpj string) (dto.RenovacaoCertificadoDto, error)
	FetchCompanyInfo(ctx context.Context, cnpj string) (accounting_acessor.CompanyInfo, error)
	CompleteMit(ctx context.Context, cnpj, competence string) error
	FetchImpostosAPagar(ctx context.Context, cnpj, competence string) (declaracaoModels.ImpostosAPagar, error)
}
