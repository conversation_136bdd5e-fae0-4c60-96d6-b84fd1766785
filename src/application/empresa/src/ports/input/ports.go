package input

import (
	"agilize/src/application/empresa/adapters/dto"
	"agilize/src/application/empresa/src/domain/models"
	"context"
)

type RenovacaoCertificadoService interface {
	FindOneById(context.Context, string) (models.RenovacaoCertificado, error)
	GetOrCreate(context.Context, dto.RenovacaoCertificadoDto) (models.RenovacaoCertificado, bool, error)
	Get(context.Context, string) (models.RenovacaoCertificado, error)
	Create(context.Context, dto.RenovacaoCertificadoDto) (models.RenovacaoCertificado, bool, error)
	GenerateVoucher(ctx context.Context, renovacaoCertificado models.RenovacaoCertificado) error
}
