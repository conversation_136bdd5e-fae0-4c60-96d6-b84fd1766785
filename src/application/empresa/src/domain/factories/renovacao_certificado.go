package factories

import (
	"agilize/src/application/empresa/adapters/dto"
	"agilize/src/application/empresa/src/domain/models"
	"agilize/src/application/empresa/src/ports/input"
	"agilize/src/shared/cryptus/uuid"
	"context"
	"fmt"
	"time"
)

type RenovacaoCertificadoFactory struct {
	empresaRepository input.EmpresaRepository
}

func NewRenovacaoCertificadoFactory(empresRepository input.EmpresaRepository) RenovacaoCertificadoFactory {
	return RenovacaoCertificadoFactory{
		empresaRepository: empresRepository,
	}
}

func (rcf RenovacaoCertificadoFactory) Create(dto dto.RenovacaoCertificadoDto) (models.RenovacaoCertificado, error) {
	empresa, err := rcf.empresaRepository.FindOneByCnpj(context.Background(), dto.Cnpj)
	if err != nil {
		return models.RenovacaoCertificado{}, err
	}
	timeToParse := fmt.Sprintf("%s 23:59:59", dto.DataVencimento)
	parsedDate, err := time.Parse(time.DateTime, timeToParse)
	if err != nil {
		return models.RenovacaoCertificado{}, err
	}
	renovacaoCertificado := models.RenovacaoCertificado{
		Id:                uuid.NewV4(),
		ProcessInstanceId: dto.ProcessInstanceId,
		Empresa:           empresa,
		IsGratuito:        dto.IsGratuito,
		IsInadimplente:    dto.IsInadimplente,
		Status:            models.NAO_INICIADO,
		DataVencimento:    parsedDate,
	}
	return renovacaoCertificado, nil
}
