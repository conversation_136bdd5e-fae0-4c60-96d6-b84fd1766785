package factories

import (
	"agilize/mocks/empresa"
	dto2 "agilize/src/application/empresa/adapters/dto"
	"agilize/src/application/empresa/src/domain/models"
	"agilize/src/shared/cryptus/uuid"
	"agilize/src/shared/testify"
	"agilize/src/shared/testify/assert"
	"testing"
	"time"

	"go.uber.org/mock/gomock"
)

func TestCreate(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testify.It(t, "Testa se cria correto para emails", func(t *testing.T) {
		// Given
		cnpj := "111111111111"
		empresaMockRepository := empresa.NewMockEmpresaRepository(ctrl)
		renovacaoCertificadoFactory := NewRenovacaoCertificadoFactory(empresaMockRepository)
		empresa := models.Empresa{
			ID:   uuid.NewV4(),
			CNPJ: cnpj,
		}
		dto := dto2.RenovacaoCertificadoDto{
			Cnpj:           cnpj,
			DataVencimento: "2024-09-06",
			IsGratuito:     true,
			IsInadimplente: false,
		}
		empresaMockRepository.EXPECT().FindOneByCnpj(gomock.Any(), cnpj).Return(empresa, nil).AnyTimes()

		// When
		renovacaoCertificado, err := renovacaoCertificadoFactory.Create(dto)

		assert.Equal(t, err, nil)
		assert.NotNil(t, renovacaoCertificado.Id)
		assert.Equal(t, renovacaoCertificado.Empresa.CNPJ, "111111111111")
		assert.Equal(t, renovacaoCertificado.IsGratuito, true)
		assert.Equal(t, renovacaoCertificado.DataVencimento.Format(time.DateOnly), "2024-09-06")
	})
}
