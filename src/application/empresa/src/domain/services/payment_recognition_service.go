package services

import (
	"agilize/src/application/empresa/src/domain/models"
	"agilize/src/application/empresa/src/ports/input"
	"context"
	"fmt"
	"log"
)

type paymentRecognitionService struct {
	certificadoRepository       input.CertificadoRepository
	empresaRepository           input.EmpresaRepository
	renovacaoCertificadoService input.RenovacaoCertificadoService
}

// NewPaymentRecognitionService creates a new payment recognition service
func NewPaymentRecognitionService(
	certificadoRepository input.CertificadoRepository,
	empresaRepository input.EmpresaRepository,
	renovacaoCertificadoService input.RenovacaoCertificadoService,
) input.PaymentRecognitionService {
	return &paymentRecognitionService{
		certificadoRepository:       certificadoRepository,
		empresaRepository:           empresaRepository,
		renovacaoCertificadoService: renovacaoCertificadoService,
	}
}

// ProcessPaymentEvent processes a payment event and updates the certificate renewal if applicable
func (s *paymentRecognitionService) ProcessPaymentEvent(ctx context.Context, event models.PaymentEvent) error {
	log.Printf("Processing payment event %s for company %s (CNPJ: %s)", event.ID, event.CompanyID, event.CNPJ)

	// Validate CNPJ
	if !event.IsValidCNPJ() {
		log.Printf("Invalid CNPJ %s for payment event %s", event.CNPJ, event.ID)
		return fmt.Errorf("invalid CNPJ %s", event.CNPJ)
	}

	// Find the company's certificate renewal
	renovacaoCertificado, err := s.certificadoRepository.FindOneValidRenovacaoCertificadoByCnpj(ctx, event.CNPJ)
	if err != nil {
		log.Printf("Error finding certificate renewal for CNPJ %s: %v", event.CNPJ, err)
		return fmt.Errorf("error finding certificate renewal for CNPJ %s: %w", event.CNPJ, err)
	}

	// Simplificando para apenas verificar se o voucher está vazio e gerá-lo se necessário
	if renovacaoCertificado.Voucher == "" {
		log.Printf("Generating voucher for certificate renewal of CNPJ %s", event.CNPJ)
		err = s.renovacaoCertificadoService.GenerateVoucher(ctx, renovacaoCertificado)
		if err != nil {
			log.Printf("Error generating voucher for CNPJ %s: %v", event.CNPJ, err)
			return fmt.Errorf("error generating voucher: %w", err)
		}
	}

	log.Printf("Successfully processed payment event %s for CNPJ %s", event.ID, event.CNPJ)
	return nil
}
