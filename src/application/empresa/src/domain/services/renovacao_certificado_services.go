package services

import (
	"agilize/src/application/empresa/adapters/dto"
	"agilize/src/application/empresa/src/domain/factories"
	"agilize/src/application/empresa/src/domain/models"
	"agilize/src/application/empresa/src/ports/input"
	"agilize/src/drivers/consult_brasil"
	"agilize/src/shared/logger"
	"context"
	"errors"
	"fmt"
)

type renovacaoCertificadoService struct {
	certificadoRepository       input.CertificadoRepository
	empresaRepository           input.EmpresaRepository
	renovacaoCertificadoFactory factories.RenovacaoCertificadoFactory
	consultBrasil               *consult_brasil.ConsultBrasil
	logger                      logger.Logger
}

func NewRenovacaoCertificadoService(certificadoRepository input.CertificadoRepository, empresRepository input.EmpresaRepository) input.RenovacaoCertificadoService {
	return &renovacaoCertificadoService{
		certificadoRepository:       certificadoRepository,
		empresaRepository:           empresRepository,
		renovacaoCertificadoFactory: factories.NewRenovacaoCertificadoFactory(empresRepository),
		consultBrasil:               consult_brasil.NewConsultBrasil(),
		logger:                      logger.GetInfoLogger(),
	}
}

func (rcs *renovacaoCertificadoService) GetOrCreate(c context.Context, dto dto.RenovacaoCertificadoDto) (models.RenovacaoCertificado, bool, error) {
	renovacaoCertificado, err := rcs.certificadoRepository.FindOneValidRenovacaoCertificadoByCnpj(c, dto.Cnpj)
	if err != nil {
		return models.RenovacaoCertificado{}, false, err
	}

	if renovacaoCertificado.Id == "" {
		newRenovacaoCertificado, err := rcs.renovacaoCertificadoFactory.Create(dto)
		if err != nil {
			return models.RenovacaoCertificado{}, true, err
		}
		newRenovacaoCertificado, err = rcs.certificadoRepository.CreateRenovacaoCertificado(c, newRenovacaoCertificado)
		if err != nil {
			return models.RenovacaoCertificado{}, true, err
		}
		return newRenovacaoCertificado, true, nil
	}

	return renovacaoCertificado, false, nil
}

func (rcs *renovacaoCertificadoService) Get(c context.Context, cnpj string) (models.RenovacaoCertificado, error) {
	renovacaoCertificado, err := rcs.certificadoRepository.FindOneValidRenovacaoCertificadoByCnpj(c, cnpj)
	if err != nil {
		return models.RenovacaoCertificado{}, err
	}

	return renovacaoCertificado, nil
}

func (rcs *renovacaoCertificadoService) Create(c context.Context, dto dto.RenovacaoCertificadoDto) (models.RenovacaoCertificado, bool, error) {

	newRenovacaoCertificado, err := rcs.renovacaoCertificadoFactory.Create(dto)
	if err != nil {
		return models.RenovacaoCertificado{}, true, err
	}
	newRenovacaoCertificado, err = rcs.certificadoRepository.CreateRenovacaoCertificado(c, newRenovacaoCertificado)
	if err != nil {
		return models.RenovacaoCertificado{}, true, err
	}
	return newRenovacaoCertificado, true, nil
}

func (rcs *renovacaoCertificadoService) FindOneById(c context.Context, id string) (models.RenovacaoCertificado, error) {
	renovacaoCertificado, err := rcs.certificadoRepository.FindOneRenovacaoCertificadoById(c, id)
	if err != nil {
		return models.RenovacaoCertificado{}, err
	}

	if renovacaoCertificado.Id == "" {
		msg := fmt.Sprintf("Não foi encontrado uma renovação de certificado digital para o ID %s - 1726083253", id)
		err := errors.New(msg)
		return models.RenovacaoCertificado{}, err
	}

	return renovacaoCertificado, nil
}

// GenerateVoucher generates a voucher for the certificate renewal using ConsultBrasil
func (rcs *renovacaoCertificadoService) GenerateVoucher(ctx context.Context, renovacaoCertificado models.RenovacaoCertificado) error {
	if renovacaoCertificado.Voucher != "" {
		return nil
	}

	empresa, err := rcs.empresaRepository.FindOneByCnpj(ctx, renovacaoCertificado.Empresa.CNPJ)
	if err != nil {
		rcs.logger.Error(fmt.Sprintf("Erro ao buscar empresa %s: %v", renovacaoCertificado.Empresa.CNPJ, err))
		return err
	}

	voucherResponse, err := rcs.consultBrasil.GerarVoucher(
		renovacaoCertificado.Empresa.CNPJ,
		empresa.Name,
		empresa.NomeFantasia,
		empresa.Cep,
	)
	if err != nil {
		rcs.logger.Error(fmt.Sprintf("Erro ao gerar voucher para empresa %s: %v", renovacaoCertificado.Empresa.CNPJ, err))
		return err
	}

	renovacaoCertificado.Voucher = voucherResponse.Code
	renovacaoCertificado.Identifier = voucherResponse.Id

	// regardless of whether it's free or not
	renovacaoCertificado.ExibirLinkEmissao = true
	rcs.logger.Info(fmt.Sprintf("Exibir link emissão alterado para true para empresa %s", renovacaoCertificado.Empresa.CNPJ))

	_, err = rcs.certificadoRepository.UpdateRenovacaoCertificado(ctx, renovacaoCertificado)
	if err != nil {
		return err
	}

	rcs.logger.Info(fmt.Sprintf("Voucher gerado com sucesso para cnpj: %s", renovacaoCertificado.Empresa.CNPJ))
	return nil
}
