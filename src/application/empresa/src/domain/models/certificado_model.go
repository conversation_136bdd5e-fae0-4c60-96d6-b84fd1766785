package models

import "time"

type RenovacaoCertificadoStatus int

const (
	NAO_INICIADO           RenovacaoCertificadoStatus = 0
	EXECUTANDO             RenovacaoCertificadoStatus = 1
	FINALIZADO             RenovacaoCertificadoStatus = 3
	INADIMPLENTE           RenovacaoCertificadoStatus = 4
	AGUARDANDO_PAGAMENTO   RenovacaoCertificadoStatus = 5
	AGUARDANDO_CERTIFICADO RenovacaoCertificadoStatus = 6
)

type RenovacaoCertificado struct {
	Id                     string
	ProcessInstanceId      string
	Empresa                Empresa
	IsGratuito             bool
	IsInadimplente         bool
	DataVencimento         time.Time
	Voucher                string
	Identifier             int64
	Status                 RenovacaoCertificadoStatus
	ExibirLinkEmissao      bool
	FaturaPersolanizadaId  string
	FaturaPersolanizadaURI string
}
