package models

import (
	"time"
)

type Cobranca struct {
	ID                    string
	Type                  int
	Company               Empresa
	TotalValue            float64
	Description           string
	Quantity              int
	InitialPaymentValue   float64
	InitialPaymentDueDate time.Time
	StartDate             time.Time
	CobrancaItens         []CobrancaItem
	FaturaPersonalizada   FaturaPersonalizada
}

type CobrancaItem struct {
	Product  string `json:"product"`
	Quantity int    `json:"quantity"`
}

type FaturaPersonalizada struct {
	URI      string
	Identity string
}
