package models

import (
	"agilize/src/shared/stringfy"
	"time"
)

// PaymentEvent represents a payment event received from RabbitMQ
type PaymentEvent struct {
	ID                    string    `json:"id"`
	FaturaPersonalizadaID string    `json:"fatura_personalizada_id"`
	CompanyID             string    `json:"company_id"`
	CNPJ                  string    `json:"company_cnpj"`
	Amount                float64   `json:"total_amount"`
	PaymentDate           time.Time `json:"payment_date"`
	PaymentMethod         string    `json:"payment_method"`
	Description           string    `json:"description"`
	Status                string    `json:"status"`
	CreatedAt             time.Time `json:"timestamp"`
}

// IsForCertificateRenewal checks if the payment is related to certificate renewal
func (p *PaymentEvent) IsForCertificateRenewal() bool {
	// to avoid missing any payments that might have different descriptions
	return true
}

// IsValidCNPJ checks if the CNPJ is valid
func (p *PaymentEvent) IsValidCNPJ() bool {
	// Using the shared stringfy package to validate CNPJ
	return stringfy.IsValidCNPJ(p.CNPJ)
}
