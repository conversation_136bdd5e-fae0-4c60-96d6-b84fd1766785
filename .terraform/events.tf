resource "aws_ecs_task_definition" "schedule" {
  for_each                 = { for idx, event in(local.env == "prd" ? local.events : []) : idx => event }
  family                   = "${local.prefix}-${local.name}-${each.value.name}"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = each.value.cpu
  memory                   = each.value.memory
  execution_role_arn       = aws_iam_role.this.arn
  container_definitions = jsonencode(
    [
      {
        command     = each.value.command
        cpu         = each.value.cpu - local.datadog_cpu
        environment = lookup(each.value, "environments", [])
        image       = "${local.image}:${local.image_tag}"
        name        = each.value.name
        memory      = each.value.memory - local.datadog_memory

        logConfiguration = {
          logDriver = "awsfirelens"
          options = {
            "Name"       = "datadog"
            "apikey"     = "********************************"
            "dd_service" = each.value.name
            "dd_source"  = "golang"
            "dd_tags"    = "env:${local.env}"
            "provider"   = "ecs"
          }
        }

        tags = [
          for key, value in merge(local.tags, { Name = "${local.prefix}-${local.name}-${each.value.name}" }) : {
            key   = key
            value = value
          }
        ]
      },
      {
        cpu       = local.datadog_cpu
        essential = true
        image     = "amazon/aws-for-fluent-bit:latest"
        memory    = local.datadog_memory
        name      = "log_router"

        firelensConfiguration = {
          type = "fluentbit"
          options = {
            "config-file-type"        = "file"
            "config-file-value"       = "/fluent-bit/configs/parse-json.conf"
            "enable-ecs-log-metadata" = "true"
          }
        }

        tags = [
          for key, value in merge(local.tags, { Name = "${local.prefix}-${local.name}-${each.value.name}-datadog" }) : {
            key   = key
            value = value
          }
        ]
      }
    ]
  )

  tags = merge(local.tags, {
    Name = "${local.prefix}-${local.name}-${each.value.name}"
  })
}

data "aws_ecs_task_definition" "schedule" {
  for_each        = { for idx, event in(local.env == "prd" ? local.events : []) : idx => event }
  task_definition = aws_ecs_task_definition.schedule[each.key].family
}

resource "aws_cloudwatch_event_rule" "schedule" {
  for_each            = { for idx, event in(local.env == "prd" ? local.events : []) : idx => event }
  name                = "${local.prefix}-${local.name}-${each.value.name}"
  description         = "Runs Fargate Task ${local.prefix}-${local.name}-${each.value.name}: ${each.value.rule_schedule_expression}"
  schedule_expression = each.value.rule_schedule_expression

  tags = merge(local.tags, {
    Name = "${local.prefix}-${local.name}-${each.value.name}"
  })
}

resource "aws_cloudwatch_event_target" "without_override" {
  for_each  = { for idx, event in(local.env == "prd" ? local.events : []) : idx => event }
  target_id = substr("${local.prefix}-${local.name}-${each.value.name}", 0, 64)
  arn       = aws_ecs_cluster.this.arn
  rule      = aws_cloudwatch_event_rule.schedule[each.key].name
  role_arn  = aws_iam_role.cloudwatch_events_role.arn

  ecs_target {
    launch_type         = "FARGATE"
    task_count          = each.value.num_tasks
    task_definition_arn = aws_ecs_task_definition.schedule[each.key].arn
    platform_version    = "1.4.0"

    network_configuration {
      assign_public_ip = false
      security_groups  = [module.security_group[0].security_group_id]
      subnets          = data.aws_subnets.this.ids
    }
  }
}