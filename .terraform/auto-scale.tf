resource "aws_appautoscaling_target" "this" {
  for_each = { for idx, service in local.services : idx => service }

  max_capacity       = each.value.max_instances
  min_capacity       = each.value.min_instances
  resource_id        = "service/${aws_ecs_cluster.this.name}/${aws_ecs_service.this[each.key].name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"

  tags = merge(local.tags, {
    Name = "${local.prefix}-${local.name}-${each.value.name}"
  })
}

#####################################################
###################### Scale Up #####################
#####################################################
resource "aws_cloudwatch_metric_alarm" "service_cpu_high" {
  for_each = { for idx, service in local.services : idx => service }

  alarm_name          = "${local.prefix}-${local.name}-${each.value.name}-cpu-utilization-high"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = "10"
  statistic           = "Average"
  threshold           = each.value.cpu_max_percent
  alarm_description   = "Alarme para quando o uso de CPU ultrapassa ${each.value.cpu_max_percent}%"
  alarm_actions       = [aws_appautoscaling_policy.scale_up[each.key].arn]

  tags = merge(local.tags, {
    Name = "${local.prefix}-${local.name}-${each.value.name}-cpu-utilization-high"
  })

  dimensions = {
    ClusterName = aws_ecs_cluster.this.name
    ServiceName = aws_ecs_service.this[each.key].name
  }
}

resource "aws_cloudwatch_metric_alarm" "service_memory_high" {
  for_each = { for idx, service in local.services : idx => service }

  alarm_name          = "${local.prefix}-${local.name}-${each.value.name}-memory-utilization-high"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = "10"
  statistic           = "Average"
  threshold           = each.value.memory_max_percent
  alarm_description   = "Alarme para quando o uso de Memória ultrapassa ${each.value.memory_max_percent}%"
  alarm_actions       = [aws_appautoscaling_policy.scale_up[each.key].arn]

  tags = merge(local.tags, {
    Name = "${local.prefix}-${local.name}-${each.value.name}-memory-utilization-high"
  })

  dimensions = {
    ClusterName = aws_ecs_cluster.this.name
    ServiceName = aws_ecs_service.this[each.key].name
  }
}

resource "aws_appautoscaling_policy" "scale_up" {
  for_each = { for idx, service in local.services : idx => service }

  name               = "${local.prefix}-${local.name}-${each.value.name}-scale-up"
  resource_id        = aws_appautoscaling_target.this[each.key].resource_id
  service_namespace  = aws_appautoscaling_target.this[each.key].service_namespace
  scalable_dimension = aws_appautoscaling_target.this[each.key].scalable_dimension

  step_scaling_policy_configuration {
    adjustment_type         = "ChangeInCapacity"
    cooldown                = "60"
    metric_aggregation_type = "Maximum"

    step_adjustment {
      metric_interval_lower_bound = 0
      scaling_adjustment          = 1
    }
  }

  depends_on = [
    aws_appautoscaling_target.this
  ]
}

#####################################################
##################### Scale Down ####################
#####################################################
resource "aws_cloudwatch_metric_alarm" "service_cpu_down" {
  for_each = { for idx, service in local.services : idx => service }

  alarm_name          = "${local.prefix}-${local.name}-${each.value.name}-cpu-utilization-down"
  comparison_operator = "LessThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = "10"
  statistic           = "Average"
  threshold           = each.value.cpu_min_percent
  alarm_description   = "Alarme para quando o uso de CPU está abaixo de ${each.value.cpu_min_percent}%"
  alarm_actions       = [aws_appautoscaling_policy.scale_down[each.key].arn]

  tags = merge(local.tags, {
    Name = "${local.prefix}-${local.name}-${each.value.name}-cpu-utilization-down"
  })

  dimensions = {
    ClusterName = aws_ecs_cluster.this.name
    ServiceName = aws_ecs_service.this[each.key].name
  }
}

resource "aws_cloudwatch_metric_alarm" "service_memory_down" {
  for_each = { for idx, service in local.services : idx => service }

  alarm_name          = "${local.prefix}-${local.name}-${each.value.name}-memory-utilization-down"
  comparison_operator = "LessThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = "10"
  statistic           = "Average"
  threshold           = each.value.memory_min_percent
  alarm_description   = "Alarme para quando o uso de Memória está abaixo de ${each.value.memory_min_percent}%"
  alarm_actions       = [aws_appautoscaling_policy.scale_down[each.key].arn]

  tags = merge(local.tags, {
    Name = "${local.prefix}-${local.name}-${each.value.name}-cpu-utilization-down"
  })

  dimensions = {
    ClusterName = aws_ecs_cluster.this.name
    ServiceName = aws_ecs_service.this[each.key].name
  }
}

resource "aws_appautoscaling_policy" "scale_down" {
  for_each = { for idx, service in local.services : idx => service }

  name               = "${local.prefix}-${local.name}-${each.value.name}-scale-down"
  resource_id        = aws_appautoscaling_target.this[each.key].resource_id
  service_namespace  = aws_appautoscaling_target.this[each.key].service_namespace
  scalable_dimension = aws_appautoscaling_target.this[each.key].scalable_dimension

  step_scaling_policy_configuration {
    adjustment_type         = "ChangeInCapacity"
    cooldown                = "60"
    metric_aggregation_type = "Maximum"

    step_adjustment {
      metric_interval_lower_bound = 0
      scaling_adjustment          = -1
    }
  }

  depends_on = [
    aws_appautoscaling_target.this
  ]
}