resource "aws_vpc_peering_connection" "agz_to_core" {
  auto_accept   = true
  peer_owner_id = local.account_id
  peer_vpc_id   = data.aws_vpcs.core.ids[0]
  vpc_id        = data.aws_vpcs.this.ids[0]

  tags = merge({
    Name = "${local.prefix}-to-${local.env}-core"
  }, local.tags)
}

resource "aws_route" "route_to_legacy_acessor" {
  route_table_id            = data.aws_route_table.this.id
  destination_cidr_block    = data.aws_vpc.core.cidr_block
  vpc_peering_connection_id = aws_vpc_peering_connection.agz_to_core.id
}

resource "aws_route" "route_from_legacy_acessor" {
  route_table_id            = data.aws_route_table.legacy_acessor.id
  destination_cidr_block    = data.aws_vpc.this.cidr_block
  vpc_peering_connection_id = aws_vpc_peering_connection.agz_to_core.id
}