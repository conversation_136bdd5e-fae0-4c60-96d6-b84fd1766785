data "aws_lb" "this" {
  name = "${local.prefix}-load-balancer"
}

data "aws_lb_listener" "https" {
  load_balancer_arn = data.aws_lb.this.arn
  port              = 443
}

data "aws_region" "current" {}

data "aws_route_table" "this" {
  subnet_id = data.aws_subnets.this.ids[0]
}

data "aws_route_table" "legacy_acessor" {
  subnet_id = data.aws_subnets.legacy_acessor.ids[0]
}

data "aws_route53_zone" "this" {
  name = "agilize.com.br."
}

data "aws_service_discovery_dns_namespace" "private" {
  name = local.env == "prd" ? "${local.project_name}.local" : "${local.env}.${local.project_name}.local"
  type = "DNS_PRIVATE"
}

data "aws_subnets" "this" {
  filter {
    name   = "vpc-id"
    values = data.aws_vpcs.this.ids
  }

  filter {
    name = "tag:Name"
    values = [
      "${local.prefix}-backend-*"
    ]
  }
}

data "aws_subnets" "legacy_acessor" {
  filter {
    name   = "vpc-id"
    values = data.aws_vpcs.core.ids
  }

  filter {
    name = "tag:Name"
    values = [
      "${local.env}-core-subnet-acesso-legado-*"
    ]
  }
}

data "aws_subnet" "this" {
  for_each = { for key, subnet_id in data.aws_subnets.this.ids : key => subnet_id }

  id = each.value
}

data "aws_vpcs" "core" {
  tags = {
    Environment = local.env
    Name        = "${local.env}-core"
  }
}

data "aws_vpcs" "this" {
  tags = {
    Environment = local.env
    Name        = "${local.prefix}"
  }
}

data "aws_vpc" "this" {
  id = data.aws_vpcs.this.ids[0]
}

data "aws_vpc" "core" {
  id = data.aws_vpcs.core.ids[0]
}