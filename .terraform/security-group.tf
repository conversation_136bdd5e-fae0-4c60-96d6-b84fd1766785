module "security_group" {
  source  = "terraform-aws-modules/security-group/aws"
  version = "~> 4.0"

  for_each = { for idx, service in local.services : idx => service }

  create = true

  name        = "${local.prefix}-${local.name}-${each.value.name}"
  description = "Politicas de seg do micro-servico ${local.name}/${each.value.name} para ${local.prefix}"
  vpc_id      = data.aws_vpcs.this.ids[0]

  ingress_with_cidr_blocks = can(each.value.port) ? [{
    from_port   = each.value.port
    to_port     = each.value.port
    protocol    = each.value.protocol != "HTTP" ? each.value.protocol : "TCP"
    description = "Libera entrada para o protocolo ${each.value.protocol != "HTTP" ? each.value.protocol : "TCP"} ao svc ${each.value.name} na porta ${each.value.port}"
    cidr_blocks = "0.0.0.0/0"
  }] : []

  egress_with_cidr_blocks = [{
    from_port                = "0"
    to_port                  = "0"
    protocol                 = "-1"
    description              = "Libera saida para todos os protocolos ao svc ${each.value.name} em todas as portas"
    source_security_group_id = "0.0.0.0/0"
  }]

  tags = merge(local.tags, {
    Name = "${local.prefix}-${local.name}-${each.value.name}"
  })
}