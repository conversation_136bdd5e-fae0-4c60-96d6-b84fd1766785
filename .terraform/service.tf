resource "aws_ecs_cluster" "this" {
  name = "${local.prefix}-${var.name}-cluster"
  tags = merge(local.tags, {
    Name = "${local.prefix}-${var.name}-cluster"
  })

  setting {
    name  = "containerInsights"
    value = local.env == "prd" ? "enabled" : "disabled"
  }
}

resource "aws_ecs_service" "this" {
  for_each = { for idx, service in local.services : idx => service }

  cluster                            = aws_ecs_cluster.this.id
  name                               = "${local.prefix}-${local.name}-${each.value.name}"
  deployment_maximum_percent         = each.value.deployment_maximum_percent
  deployment_minimum_healthy_percent = each.value.deployment_minimum_healthy_percent
  desired_count                      = each.value.min_instances
  launch_type                        = "FARGATE"
  platform_version                   = "1.4.0"
  task_definition                    = "${aws_ecs_task_definition.this[each.key].family}:${max(aws_ecs_task_definition.this[each.key].revision, data.aws_ecs_task_definition.this[each.key].revision)}"

  dynamic "load_balancer" {
    for_each = can(each.value.port) ? [each.value] : []

    content {
      target_group_arn = aws_lb_target_group.webapp.arn
      container_name   = each.value.name
      container_port   = each.value.port
    }
  }

  network_configuration {
    security_groups  = [module.security_group[each.key].security_group_id]
    subnets          = data.aws_subnets.this.ids
    assign_public_ip = false
  }

  service_registries {
    registry_arn = aws_service_discovery_service.webapp.arn
  }

  tags = merge(local.tags, {
    Name        = local.name
    ServiceName = each.value.name
  })

  depends_on = [
    aws_lb_target_group.webapp,
    aws_service_discovery_service.webapp,
    data.aws_subnets.this
  ]
}