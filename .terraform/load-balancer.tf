resource "aws_lb_target_group" "webapp" {
  name        = substr("${local.prefix}-${local.name}-webapp", 0, 32)
  port        = local.webapp_health_check_port
  protocol    = local.webapp_health_check_protocol
  vpc_id      = data.aws_vpcs.this.ids[0]
  target_type = "ip"

  health_check {
    interval          = local.webapp_health_check_interval
    matcher           = local.webapp_health_check_matcher
    path              = local.webapp_health_check_path
    protocol          = local.webapp_health_check_protocol
    healthy_threshold = local.webapp_health_check_threshold
    timeout           = local.webapp_health_check_timeout
  }

  tags = merge(local.tags, {
    Name = "${local.prefix}-${local.name}-webapp"
  })
}

resource "aws_lb_listener_rule" "forward" {
  listener_arn = data.aws_lb_listener.https.arn
  priority     = local.webapp_load_balancer_priority

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.webapp.arn
  }

  condition {
    path_pattern {
      values = ["/*"]
    }
  }

  condition {
    host_header {
      values = [
        local.env == "prd" ? "${local.name}.${data.aws_route53_zone.this.name}" : "${local.name}-${local.env}.${data.aws_route53_zone.this.name}"
      ]
    }
  }
}