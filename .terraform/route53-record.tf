resource "aws_route53_record" "webapp" {
  zone_id = data.aws_route53_zone.this.id
  name    = local.env == "prd" ? "${local.name}.${data.aws_route53_zone.this.name}" : "${local.name}-${local.env}.${data.aws_route53_zone.this.name}"
  type    = "A"

  alias {
    name                   = data.aws_lb.this.dns_name
    zone_id                = data.aws_lb.this.zone_id
    evaluate_target_health = true
  }

  depends_on = [
    aws_lb_target_group.webapp
  ]
}
