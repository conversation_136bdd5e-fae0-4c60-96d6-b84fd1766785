resource "aws_service_discovery_service" "webapp" {
  name = "${local.name}-webapp"

  tags = merge(local.tags, {
    Name = "${local.prefix}-${local.name}-webapp"
  })

  dns_config {
    namespace_id = data.aws_service_discovery_dns_namespace.private.id

    dns_records {
      ttl  = 10
      type = "A"
    }

    routing_policy = "MULTIVALUE"
  }

  health_check_custom_config {
    failure_threshold = 1
  }
}
