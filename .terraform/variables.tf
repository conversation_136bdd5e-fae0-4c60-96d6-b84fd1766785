variable "acessor_hostname" {
  type        = string
  description = "Hostname do Accessor"
}

variable "account_id" {
  type        = string
  description = "ID da conta a ser usada para implantar o cluster"
}

variable "accounting_db_hostname" {
  type        = string
  description = "Hostname do banco de dados do accounting"
}

variable "accounting_db_name" {
  type        = string
  description = "Nome do banco de dados do accounting"
}

variable "accounting_db_password" {
  type        = string
  description = "Senha do usuário p banco de dados do accounting"
}

variable "accounting_db_port" {
  type        = string
  description = "Porta do banco de dados do accounting"
  default     = "5432"
}

variable "accounting_db_username" {
  type        = string
  description = "Nome do usuário p banco de dados do accounting"
}

variable "aws_access_key_id_bucket" {
  type        = string
  description = "Access Key ID para acesso ao bucket"
}

variable "aws_secret_access_key_bucket" {
  type        = string
  description = "Secret Access Key para acesso ao bucket"
}

variable "camunda_auth_token" {
  type        = string
  description = "Auth token do camunda"
}

variable "camunda_hostname" {
  type        = string
  description = "Hostname do camunda"
}

variable "certificate_private_key" {
  type        = string
  description = "Chave privada do certificado"
}

variable "cidrs" {
  type        = map(string)
  description = "Bloco de endereços IP"
  default = {
    dev = "**********/16"
    hml = "**********/16"
    prd = "**********/16"
  }
}

variable "env" {
  type        = string
  description = "Ambiente onde será implantado o cluster"
}

variable "image_tag" {
  type        = string
  description = "Tag da imagem que deverá ser usada"
  default     = "latest"
}

variable "name" {
  type        = string
  description = "Nome do serviço"
  default     = "api-accounting-new"
}

variable "project_name" {
  type        = string
  description = "Projeto onde o cluster será implantado"
}

variable "region" {
  type        = string
  description = "Região onde o cluster deverá ser implantado"
  default     = "us-east-1"
}

variable "two_captch_api_key" {
  type        = string
  description = "Chave do TWO Captcha"
}

variable "cap_monster_api_key" {
  type        = string
  description = "Chave do Capmonster"
}

variable "keycloak_public_key" {
  type        = string
  description = "Chave publica do keycloak"
}

variable "keycloak_user_provider_credential" {
  type        = string
  description = "Chave do token que prover a credencial"
}

variable "keycloak_token_principal_attribute" {
  type        = string
  description = "Chave do token que é identificador do usuário no keycloak"
}

variable "notificacao_url" {
  type        = string
  description = "URL do serviço de notificação"
}

variable "notificacao_api_key" {
  type        = string
  description = "API Key do serviço de notificação"
}

variable "keycloak_power_automate_client_id" {
  type        = string
  description = "Client id do power automate no keycloak"
}

variable "keycloak_power_automate_sub" {
  type        = string
  description = "Id de validação do power automate client"
}

variable "temporal_io_address" {
  type        = string
  description = "Chave do endereço de acesso do temporal io"
}

variable "contabil_url" {
  type        = string
  description = "URL do serviço de contabilidade"
}

variable "contabil_apikey" {
  type        = string
  description = "API Key do serviço de contabilidade"
}
