terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

locals {
  account_id                 = var.account_id
  env                        = var.env
  image                      = "${local.account_id}.dkr.ecr.${local.region}.amazonaws.com/api-accounting-new"
  image_tag                  = var.image_tag
  log_format                 = "json"
  log_level                  = var.env == "prd" ? "info" : "debug"
  name                       = var.name
  postgres_max_conn_lifetime = "480"
  postgres_max_idle_conn     = "10"
  postgres_max_open_conn     = "10"
  postgres_url               = "postgres://${var.accounting_db_username}:${var.accounting_db_password}@${var.accounting_db_hostname}/${var.accounting_db_name}"
  prefix                     = "${var.env}-${var.project_name}"
  project_name               = var.project_name
  region                     = var.region
  retention_logs_in_days     = var.env == "prd" ? 14 : 7
  datadog_cpu                = 256
  datadog_memory             = 512

  webapp_health_check_interval  = 60
  webapp_health_check_matcher   = 200
  webapp_health_check_path      = "/health"
  webapp_health_check_port      = 80
  webapp_health_check_protocol  = "HTTP"
  webapp_health_check_threshold = 3
  webapp_health_check_timeout   = 5
  webapp_load_balancer_priority = 40

  // Definições de Processamento e memória das tasks
  webapp_cpu           = 512
  webapp_max_instances = var.env == "prd" ? 2 : 1
  webapp_memory        = (local.webapp_cpu) * 2
  webapp_min_instances = var.env == "prd" ? 1 : 1
  worker_cpu           = 512
  worker_max_instances = var.env == "prd" ? 10 : 1
  worker_memory        = (local.worker_cpu) * 2
  worker_min_instances = var.env == "prd" ? 10 : 1

  events = [
    {
      command                  = ["/worker", "-command", "iniciar_declaracoes"]
      cpu                      = local.worker_cpu
      memory                   = local.worker_memory
      name                     = "iniciar-declaracoes"
      num_tasks                = 1
      rule_schedule_expression = "cron(40 3 * * ? *)"
      environments             = [
        {
          name  = "DEBUG"
          value = local.env != "prd" ? "true" : "false"
        },
        {
          name  = "HTTP_ADDRESS"
          value = "0.0.0.0"
        },
        {
          name  = "HTTP_PORT"
          value = "80"
        },
        {
          name  = "HTTP_ALLOW_ORIGINS"
          value = "*"
        },
        {
          name  = "POSTGRES_URL"
          value = local.postgres_url
        },
        {
          name  = "POSTGRES_MAX_OPEN_CONN"
          value = local.postgres_max_open_conn
        },
        {
          name  = "POSTGRES_MAX_IDLE_CONN"
          value = local.postgres_max_idle_conn
        },
        {
          name  = "POSTGRES_MAX_CONN_LIFETIME"
          value = local.postgres_max_conn_lifetime
        },
        {
          name  = "CAMUNDA_BASE_URL"
          value = var.camunda_hostname
        },
        {
          name  = "CAMUNDA_AUTH_TOKEN"
          value = var.camunda_auth_token
        },
        {
          name  = "ACCOUNTING_ACESSOR_URL"
          value = var.acessor_hostname
        },
        {
          name  = "AWS_ACCESS_KEY_ID"
          value = var.aws_access_key_id_bucket
        },
        {
          name  = "AWS_BUCKET"
          value = "${local.env}-core-accounting-bucket"
        },
        {
          name  = "AWS_SECRET_ACCESS_KEY"
          value = var.aws_secret_access_key_bucket
        },
        {
          name  = "AWS_DEFAULT_REGION"
          value = "us-east-1"
        },
        {
          name  = "CERTIFICATE_PRIVATE_KEY"
          value = var.certificate_private_key
        },
        {
          name  = "TWO_CAPTCHA_API_KEY"
          value = var.two_captch_api_key
        },
        {
          name  = "CAP_MONSTER_API_KEY"
          value = var.cap_monster_api_key
        },
        {
          name  = "TEMPORAL_IO_ADDRESS"
          value = var.temporal_io_address
        },
        {
          name  = "NOTIFICACAO_API_KEY"
          value = var.notificacao_api_key
        },
        {
          name  = "NOTIFICACAO_URL"
          value = var.notificacao_url
        },
        {
          name  = "APP_ENVIRONMENT"
          value = var.env
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_CLIENT_ID"
          value = var.keycloak_power_automate_client_id
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_SUB"
          value = var.keycloak_power_automate_sub
        },
        {
          name  = "LOG_LEVEL"
          value = local.log_level
        },
        {
          name  = "LOG_OUTPUT"
          value = "stdout"
        },
        {
          name  = "LOG_FORMAT"
          value = local.log_format
        },
        {
          name  = "DD_AGENT_HOST"
          value = "datadog.agilize.com.br"
        },
        {
          name  = "DD_TRACE_AGENT_PORT"
          value = "8126"
        },
        {
          name  = "DD_ENV"
          value = local.env
        },
        {
          name  = "DD_SERVICE"
          value = "api_accounting_new_iniciar_declaracoes"
        },
        {
          name  = "CONTABIL_URL"
          value = var.contabil_url
        },
        {
          name  = "CONTABIL_APIKEY"
          value = var.contabil_apikey
        }
      ]
    },
    {
      command                  = ["/worker", "-command", "update_dashboard_view"]
      cpu                      = local.worker_cpu
      memory                   = local.worker_memory
      name                     = "update-dashboard-view"
      num_tasks                = 1
      rule_schedule_expression = "cron(0/30 * * * ? *)"
      environments             = [
        {
          name  = "DEBUG"
          value = local.env != "prd" ? "true" : "false"
        },
        {
          name  = "HTTP_ADDRESS"
          value = "0.0.0.0"
        },
        {
          name  = "HTTP_PORT"
          value = "80"
        },
        {
          name  = "HTTP_ALLOW_ORIGINS"
          value = "*"
        },
        {
          name  = "POSTGRES_URL"
          value = local.postgres_url
        },
        {
          name  = "POSTGRES_MAX_OPEN_CONN"
          value = local.postgres_max_open_conn
        },
        {
          name  = "POSTGRES_MAX_IDLE_CONN"
          value = local.postgres_max_idle_conn
        },
        {
          name  = "POSTGRES_MAX_CONN_LIFETIME"
          value = local.postgres_max_conn_lifetime
        },
        {
          name  = "CAMUNDA_BASE_URL"
          value = var.camunda_hostname
        },
        {
          name  = "CAMUNDA_AUTH_TOKEN"
          value = var.camunda_auth_token
        },
        {
          name  = "ACCOUNTING_ACESSOR_URL"
          value = var.acessor_hostname
        },
        {
          name  = "AWS_ACCESS_KEY_ID"
          value = var.aws_access_key_id_bucket
        },
        {
          name  = "AWS_BUCKET"
          value = "${local.env}-core-accounting-bucket"
        },
        {
          name  = "AWS_SECRET_ACCESS_KEY"
          value = var.aws_secret_access_key_bucket
        },
        {
          name  = "AWS_DEFAULT_REGION"
          value = "us-east-1"
        },
        {
          name  = "CERTIFICATE_PRIVATE_KEY"
          value = var.certificate_private_key
        },
        {
          name  = "TWO_CAPTCHA_API_KEY"
          value = var.two_captch_api_key
        },
        {
          name  = "CAP_MONSTER_API_KEY"
          value = var.cap_monster_api_key
        },
        {
          name  = "TEMPORAL_IO_ADDRESS"
          value = var.temporal_io_address
        },
        {
          name  = "NOTIFICACAO_API_KEY"
          value = var.notificacao_api_key
        },
        {
          name  = "NOTIFICACAO_URL"
          value = var.notificacao_url
        },
        {
          name  = "APP_ENVIRONMENT"
          value = var.env
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_CLIENT_ID"
          value = var.keycloak_power_automate_client_id
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_SUB"
          value = var.keycloak_power_automate_sub
        },
        {
          name  = "LOG_LEVEL"
          value = local.log_level
        },
        {
          name  = "LOG_OUTPUT"
          value = "stdout"
        },
        {
          name  = "LOG_FORMAT"
          value = local.log_format
        },
        {
          name  = "DD_AGENT_HOST"
          value = "datadog.agilize.com.br"
        },
        {
          name  = "DD_TRACE_AGENT_PORT"
          value = "8126"
        },
        {
          name  = "DD_ENV"
          value = local.env
        },
        {
          name  = "DD_SERVICE"
          value = "api_accounting_new_update_dashboard_view"
        },
        {
          name  = "CONTABIL_URL"
          value = var.contabil_url
        },
        {
          name  = "CONTABIL_APIKEY"
          value = var.contabil_apikey
        }
      ]
    },
    {
      command                  = ["/worker", "-command", "enviar_notificacoes_agendadas"]
      cpu                      = local.worker_cpu
      memory                   = local.worker_memory
      name                     = "enviar_notificacoes_agendadas"
      num_tasks                = 1
      rule_schedule_expression = "cron(0 11 * * ? *)"
      environments             = [
        {
          name  = "DEBUG"
          value = local.env != "prd" ? "true" : "false"
        },
        {
          name  = "HTTP_ADDRESS"
          value = "0.0.0.0"
        },
        {
          name  = "HTTP_PORT"
          value = "80"
        },
        {
          name  = "HTTP_ALLOW_ORIGINS"
          value = "*"
        },
        {
          name  = "POSTGRES_URL"
          value = local.postgres_url
        },
        {
          name  = "POSTGRES_MAX_OPEN_CONN"
          value = local.postgres_max_open_conn
        },
        {
          name  = "POSTGRES_MAX_IDLE_CONN"
          value = local.postgres_max_idle_conn
        },
        {
          name  = "POSTGRES_MAX_CONN_LIFETIME"
          value = local.postgres_max_conn_lifetime
        },
        {
          name  = "CAMUNDA_BASE_URL"
          value = var.camunda_hostname
        },
        {
          name  = "CAMUNDA_AUTH_TOKEN"
          value = var.camunda_auth_token
        },
        {
          name  = "ACCOUNTING_ACESSOR_URL"
          value = var.acessor_hostname
        },
        {
          name  = "AWS_ACCESS_KEY_ID"
          value = var.aws_access_key_id_bucket
        },
        {
          name  = "AWS_BUCKET"
          value = "${local.env}-core-accounting-bucket"
        },
        {
          name  = "AWS_SECRET_ACCESS_KEY"
          value = var.aws_secret_access_key_bucket
        },
        {
          name  = "AWS_DEFAULT_REGION"
          value = "us-east-1"
        },
        {
          name  = "CERTIFICATE_PRIVATE_KEY"
          value = var.certificate_private_key
        },
        {
          name  = "TWO_CAPTCHA_API_KEY"
          value = var.two_captch_api_key
        },
        {
          name  = "CAP_MONSTER_API_KEY"
          value = var.cap_monster_api_key
        },
        {
          name  = "TEMPORAL_IO_ADDRESS"
          value = var.temporal_io_address
        },
        {
          name  = "NOTIFICACAO_API_KEY"
          value = var.notificacao_api_key
        },
        {
          name  = "NOTIFICACAO_URL"
          value = var.notificacao_url
        },
        {
          name  = "APP_ENVIRONMENT"
          value = var.env
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_CLIENT_ID"
          value = var.keycloak_power_automate_client_id
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_SUB"
          value = var.keycloak_power_automate_sub
        },
        {
          name  = "LOG_LEVEL"
          value = local.log_level
        },
        {
          name  = "LOG_OUTPUT"
          value = "stdout"
        },
        {
          name  = "LOG_FORMAT"
          value = local.log_format
        },
        {
          name  = "DD_AGENT_HOST"
          value = "datadog.agilize.com.br"
        },
        {
          name  = "DD_TRACE_AGENT_PORT"
          value = "8126"
        },
        {
          name  = "DD_ENV"
          value = local.env
        },
        {
          name  = "DD_SERVICE"
          value = "api_accounting_new_enviar_notificacoes_agendadas"
        },
        {
          name  = "CONTABIL_URL"
          value = var.contabil_url
        },
        {
          name  = "CONTABIL_APIKEY"
          value = var.contabil_apikey
        }
      ]
    },
    {
      command                  = ["/worker", "-command", "executar_renovacao_certificado_workflow"]
      cpu                      = local.worker_cpu
      memory                   = local.worker_memory
      name                     = "executar-renovacao-certificado"
      num_tasks                = 1
      rule_schedule_expression = "cron(0 0/1 * * ? *)"
      environments             = [
        {
          name  = "DEBUG"
          value = local.env != "prd" ? "true" : "false"
        },
        {
          name  = "HTTP_ADDRESS"
          value = "0.0.0.0"
        },
        {
          name  = "HTTP_PORT"
          value = "80"
        },
        {
          name  = "HTTP_ALLOW_ORIGINS"
          value = "*"
        },
        {
          name  = "POSTGRES_URL"
          value = local.postgres_url
        },
        {
          name  = "POSTGRES_MAX_OPEN_CONN"
          value = local.postgres_max_open_conn
        },
        {
          name  = "POSTGRES_MAX_IDLE_CONN"
          value = local.postgres_max_idle_conn
        },
        {
          name  = "POSTGRES_MAX_CONN_LIFETIME"
          value = local.postgres_max_conn_lifetime
        },
        {
          name  = "CAMUNDA_BASE_URL"
          value = var.camunda_hostname
        },
        {
          name  = "CAMUNDA_AUTH_TOKEN"
          value = var.camunda_auth_token
        },
        {
          name  = "ACCOUNTING_ACESSOR_URL"
          value = var.acessor_hostname
        },
        {
          name  = "AWS_ACCESS_KEY_ID"
          value = var.aws_access_key_id_bucket
        },
        {
          name  = "AWS_BUCKET"
          value = "${local.env}-core-accounting-bucket"
        },
        {
          name  = "AWS_SECRET_ACCESS_KEY"
          value = var.aws_secret_access_key_bucket
        },
        {
          name  = "AWS_DEFAULT_REGION"
          value = "us-east-1"
        },
        {
          name  = "CERTIFICATE_PRIVATE_KEY"
          value = var.certificate_private_key
        },
        {
          name  = "TWO_CAPTCHA_API_KEY"
          value = var.two_captch_api_key
        },
        {
          name  = "CAP_MONSTER_API_KEY"
          value = var.cap_monster_api_key
        },
        {
          name  = "TEMPORAL_IO_ADDRESS"
          value = var.temporal_io_address
        },
        {
          name  = "NOTIFICACAO_API_KEY"
          value = var.notificacao_api_key
        },
        {
          name  = "NOTIFICACAO_URL"
          value = var.notificacao_url
        },
        {
          name  = "APP_ENVIRONMENT"
          value = var.env
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_CLIENT_ID"
          value = var.keycloak_power_automate_client_id
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_SUB"
          value = var.keycloak_power_automate_sub
        },
        {
          name  = "LOG_LEVEL"
          value = local.log_level
        },
        {
          name  = "LOG_OUTPUT"
          value = "stdout"
        },
        {
          name  = "LOG_FORMAT"
          value = local.log_format
        },
        {
          name  = "DD_AGENT_HOST"
          value = "datadog.agilize.com.br"
        },
        {
          name  = "DD_TRACE_AGENT_PORT"
          value = "8126"
        },
        {
          name  = "DD_ENV"
          value = local.env
        },
        {
          name  = "DD_SERVICE"
          value = "api_accounting_new_executar_renovacao_certificado"
        },
        {
          name  = "CONTABIL_URL"
          value = var.contabil_url
        },
        {
          name  = "CONTABIL_APIKEY"
          value = var.contabil_apikey
        }
      ]
    },
    {
      command                  = ["/worker", "-command", "finalizar_renovacao_certificado_workflow"]
      cpu                      = local.worker_cpu
      memory                   = local.worker_memory
      name                     = "finalizar-renovacao-certificado"
      num_tasks                = 1
      rule_schedule_expression = "cron(0 0/12 * * ? *)"
      environments             = [
        {
          name  = "DEBUG"
          value = local.env != "prd" ? "true" : "false"
        },
        {
          name  = "HTTP_ADDRESS"
          value = "0.0.0.0"
        },
        {
          name  = "HTTP_PORT"
          value = "80"
        },
        {
          name  = "HTTP_ALLOW_ORIGINS"
          value = "*"
        },
        {
          name  = "POSTGRES_URL"
          value = local.postgres_url
        },
        {
          name  = "POSTGRES_MAX_OPEN_CONN"
          value = local.postgres_max_open_conn
        },
        {
          name  = "POSTGRES_MAX_IDLE_CONN"
          value = local.postgres_max_idle_conn
        },
        {
          name  = "POSTGRES_MAX_CONN_LIFETIME"
          value = local.postgres_max_conn_lifetime
        },
        {
          name  = "CAMUNDA_BASE_URL"
          value = var.camunda_hostname
        },
        {
          name  = "CAMUNDA_AUTH_TOKEN"
          value = var.camunda_auth_token
        },
        {
          name  = "ACCOUNTING_ACESSOR_URL"
          value = var.acessor_hostname
        },
        {
          name  = "AWS_ACCESS_KEY_ID"
          value = var.aws_access_key_id_bucket
        },
        {
          name  = "AWS_BUCKET"
          value = "${local.env}-core-accounting-bucket"
        },
        {
          name  = "AWS_SECRET_ACCESS_KEY"
          value = var.aws_secret_access_key_bucket
        },
        {
          name  = "AWS_DEFAULT_REGION"
          value = "us-east-1"
        },
        {
          name  = "CERTIFICATE_PRIVATE_KEY"
          value = var.certificate_private_key
        },
        {
          name  = "TWO_CAPTCHA_API_KEY"
          value = var.two_captch_api_key
        },
        {
          name  = "CAP_MONSTER_API_KEY"
          value = var.cap_monster_api_key
        },
        {
          name  = "TEMPORAL_IO_ADDRESS"
          value = var.temporal_io_address
        },
        {
          name  = "NOTIFICACAO_API_KEY"
          value = var.notificacao_api_key
        },
        {
          name  = "NOTIFICACAO_URL"
          value = var.notificacao_url
        },
        {
          name  = "APP_ENVIRONMENT"
          value = var.env
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_CLIENT_ID"
          value = var.keycloak_power_automate_client_id
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_SUB"
          value = var.keycloak_power_automate_sub
        },
        {
          name  = "LOG_LEVEL"
          value = local.log_level
        },
        {
          name  = "LOG_OUTPUT"
          value = "stdout"
        },
        {
          name  = "LOG_FORMAT"
          value = local.log_format
        },
        {
          name  = "DD_AGENT_HOST"
          value = "datadog.agilize.com.br"
        },
        {
          name  = "DD_TRACE_AGENT_PORT"
          value = "8126"
        },
        {
          name  = "DD_ENV"
          value = local.env
        },
        {
          name  = "DD_SERVICE"
          value = "api_accounting_new_finalizar_renovacao_certificado"
        },
        {
          name  = "CONTABIL_URL"
          value = var.contabil_url
        },
        {
          name  = "CONTABIL_APIKEY"
          value = var.contabil_apikey
        }
      ]
    },
    {
      command                  = ["/worker", "-command", "fix_exibir_link_emissao_certificado"],
      cpu                      = local.worker_cpu,
      memory                   = local.worker_memory,
      name                     = "fix-exibir-link-emissao-certificado",
      num_tasks                = 1,
      rule_schedule_expression = "cron(0 0/3 * * ? *)",
      environments             = [
        {
          name  = "DEBUG",
          value = local.env != "prd" ? "true" : "false"
        },
        {
          name  = "HTTP_ADDRESS",
          value = "0.0.0.0"
        },
        {
          name  = "HTTP_PORT",
          value = "80"
        },
        {
          name  = "HTTP_ALLOW_ORIGINS",
          value = "*"
        },
        {
          name  = "POSTGRES_URL",
          value = local.postgres_url
        },
        {
          name  = "POSTGRES_MAX_OPEN_CONN",
          value = local.postgres_max_open_conn
        },
        {
          name  = "POSTGRES_MAX_IDLE_CONN",
          value = local.postgres_max_idle_conn
        },
        {
          name  = "POSTGRES_MAX_CONN_LIFETIME",
          value = local.postgres_max_conn_lifetime
        },
        {
          name  = "CAMUNDA_BASE_URL",
          value = var.camunda_hostname
        },
        {
          name  = "CAMUNDA_AUTH_TOKEN",
          value = var.camunda_auth_token
        },
        {
          name  = "ACCOUNTING_ACESSOR_URL",
          value = var.acessor_hostname
        },
        {
          name  = "AWS_ACCESS_KEY_ID",
          value = var.aws_access_key_id_bucket
        },
        {
          name  = "AWS_BUCKET",
          value = "${local.env}-core-accounting-bucket"
        },
        {
          name  = "AWS_SECRET_ACCESS_KEY",
          value = var.aws_secret_access_key_bucket
        },
        {
          name  = "AWS_DEFAULT_REGION",
          value = "us-east-1"
        },
        {
          name  = "CERTIFICATE_PRIVATE_KEY",
          value = var.certificate_private_key
        },
        {
          name  = "TWO_CAPTCHA_API_KEY",
          value = var.two_captch_api_key
        },
        {
          name  = "CAP_MONSTER_API_KEY",
          value = var.cap_monster_api_key
        },
        {
          name  = "TEMPORAL_IO_ADDRESS",
          value = var.temporal_io_address
        },
        {
          name  = "NOTIFICACAO_API_KEY",
          value = var.notificacao_api_key
        },
        {
          name  = "NOTIFICACAO_URL",
          value = var.notificacao_url
        },
        {
          name  = "APP_ENVIRONMENT",
          value = var.env
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_CLIENT_ID",
          value = var.keycloak_power_automate_client_id
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_SUB",
          value = var.keycloak_power_automate_sub
        },
        {
          name  = "LOG_LEVEL"
          value = local.log_level
        },
        {
          name  = "LOG_OUTPUT"
          value = "stdout"
        },
        {
          name  = "LOG_FORMAT"
          value = local.log_format
        },
        {
          name  = "DD_AGENT_HOST"
          value = "datadog.agilize.com.br"
        },
        {
          name  = "DD_TRACE_AGENT_PORT"
          value = "8126"
        },
        {
          name  = "DD_ENV"
          value = local.env
        },
        {
          name  = "DD_SERVICE"
          value = "api_accounting_new_fix_exibir_link_emissao_certificado"
        },
        {
          name  = "CONTABIL_URL"
          value = var.contabil_url
        },
        {
          name  = "CONTABIL_APIKEY"
          value = var.contabil_apikey
        }
      ]
    }
  ]

  services = [
    {
      command                            = ["/webapp"]
      cpu                                = local.webapp_cpu
      cpu_max_percent                    = 80
      cpu_min_percent                    = 40
      deployment_maximum_percent         = 200
      deployment_minimum_healthy_percent = 100
      max_instances                      = local.webapp_max_instances
      memory                             = local.webapp_memory
      memory_max_percent                 = 80
      memory_min_percent                 = 40
      min_instances                      = local.webapp_min_instances
      name                               = "webapp"
      port                               = local.webapp_health_check_port
      protocol                           = local.webapp_health_check_protocol
      service_discovey                   = aws_service_discovery_service.webapp

      environments = [
        {
          name  = "LOG_LEVEL"
          value = local.log_level
        },
        {
          name  = "LOG_OUTPUT"
          value = "stdout"
        },
        {
          name  = "LOG_FORMAT"
          value = local.log_format
        },
        {
          name  = "DEBUG"
          value = local.env != "prd" ? "true" : "false"
        },
        {
          name  = "HTTP_ADDRESS"
          value = "0.0.0.0"
        },
        {
          name  = "HTTP_PORT"
          value = "80"
        },
        {
          name  = "HTTP_ALLOW_ORIGINS"
          value = "*"
        },
        {
          name  = "POSTGRES_URL"
          value = local.postgres_url
        },
        {
          name  = "POSTGRES_MAX_OPEN_CONN"
          value = local.postgres_max_open_conn
        },
        {
          name  = "POSTGRES_MAX_IDLE_CONN"
          value = local.postgres_max_idle_conn
        },
        {
          name  = "POSTGRES_MAX_CONN_LIFETIME"
          value = local.postgres_max_conn_lifetime
        },
        {
          name  = "CAMUNDA_BASE_URL"
          value = var.camunda_hostname
        },
        {
          name  = "CAMUNDA_AUTH_TOKEN"
          value = var.camunda_auth_token
        },
        {
          name  = "ACCOUNTING_ACESSOR_URL"
          value = var.acessor_hostname
        },
        {
          name  = "AWS_ACCESS_KEY_ID"
          value = var.aws_access_key_id_bucket
        },
        {
          name  = "AWS_BUCKET"
          value = "${local.env}-core-accounting-bucket"
        },
        {
          name  = "AWS_SECRET_ACCESS_KEY"
          value = var.aws_secret_access_key_bucket
        },
        {
          name  = "AWS_DEFAULT_REGION"
          value = "us-east-1"
        },
        {
          name  = "CERTIFICATE_PRIVATE_KEY"
          value = var.certificate_private_key
        },
        {
          name  = "TWO_CAPTCHA_API_KEY"
          value = var.two_captch_api_key
        },
        {
          name  = "CAP_MONSTER_API_KEY"
          value = var.cap_monster_api_key
        },
        {
          name  = "TEMPORAL_IO_ADDRESS"
          value = var.temporal_io_address
        },
        {
          name  = "KEYCLOAK_PUBLIC_KEY"
          value = var.keycloak_public_key
        },
        {
          name  = "KEYCLOAK_USER_PROVIDER_CREDENTIAL"
          value = var.keycloak_user_provider_credential
        },
        {
          name  = "KEYCLOAK_TOKEN_PRINCIPAL_ATTRIBUTE"
          value = var.keycloak_token_principal_attribute
        },
        {
          name  = "NOTIFICACAO_API_KEY"
          value = var.notificacao_api_key
        },
        {
          name  = "NOTIFICACAO_URL"
          value = var.notificacao_url
        },
        {
          name  = "APP_ENVIRONMENT"
          value = var.env
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_CLIENT_ID"
          value = var.keycloak_power_automate_client_id
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_SUB"
          value = var.keycloak_power_automate_sub
        },
        {
          name  = "DD_AGENT_HOST"
          value = "datadog.agilize.com.br"
        },
        {
          name  = "DD_TRACE_AGENT_PORT"
          value = "8126"
        },
        {
          name  = "DD_ENV"
          value = local.env
        },
        {
          name  = "DD_SERVICE"
          value = "api_accounting_new_webapp"
        },
        {
          name  = "CONTABIL_URL"
          value = var.contabil_url
        },
        {
          name  = "CONTABIL_APIKEY"
          value = var.contabil_apikey
        }
      ]
    },
    {
      command = [
        "/worker",
        "-rod=show",
        "-command", "declaracoes_municipais",
        "-threads", "1",
        "-maxTasks", "1",
      ]
      cpu                                = local.worker_cpu
      cpu_max_percent                    = 80
      cpu_min_percent                    = 40
      deployment_maximum_percent         = 200
      deployment_minimum_healthy_percent = 100
      max_instances                      = local.worker_max_instances
      memory                             = local.worker_memory
      memory_max_percent                 = 80
      memory_min_percent                 = 40
      min_instances                      = local.worker_min_instances
      name                               = "worker-declaracoes-municipais"

      environments = [
        {
          name  = "LOG_LEVEL"
          value = local.log_level
        },
        {
          name  = "LOG_OUTPUT"
          value = "stdout"
        },
        {
          name  = "LOG_FORMAT"
          value = local.log_format
        },
        {
          name  = "DEBUG"
          value = local.env != "prd" ? "true" : "false"
        },
        {
          name  = "HTTP_ADDRESS"
          value = "0.0.0.0"
        },
        {
          name  = "HTTP_PORT"
          value = "80"
        },
        {
          name  = "HTTP_ALLOW_ORIGINS"
          value = "*"
        },
        {
          name  = "POSTGRES_URL"
          value = local.postgres_url
        },
        {
          name  = "POSTGRES_MAX_OPEN_CONN"
          value = local.postgres_max_open_conn
        },
        {
          name  = "POSTGRES_MAX_IDLE_CONN"
          value = local.postgres_max_idle_conn
        },
        {
          name  = "POSTGRES_MAX_CONN_LIFETIME"
          value = local.postgres_max_conn_lifetime
        },
        {
          name  = "CAMUNDA_BASE_URL"
          value = var.camunda_hostname
        },
        {
          name  = "CAMUNDA_AUTH_TOKEN"
          value = var.camunda_auth_token
        },
        {
          name  = "ACCOUNTING_ACESSOR_URL"
          value = var.acessor_hostname
        },
        {
          name  = "AWS_ACCESS_KEY_ID"
          value = var.aws_access_key_id_bucket
        },
        {
          name  = "AWS_BUCKET"
          value = "${local.env}-core-accounting-bucket"
        },
        {
          name  = "AWS_SECRET_ACCESS_KEY"
          value = var.aws_secret_access_key_bucket
        },
        {
          name  = "AWS_DEFAULT_REGION"
          value = "us-east-1"
        },
        {
          name  = "CERTIFICATE_PRIVATE_KEY"
          value = var.certificate_private_key
        },
        {
          name  = "TWO_CAPTCHA_API_KEY"
          value = var.two_captch_api_key
        },
        {
          name  = "CAP_MONSTER_API_KEY"
          value = var.cap_monster_api_key
        },
        {
          name  = "TEMPORAL_IO_ADDRESS"
          value = var.temporal_io_address
        },
        {
          name  = "NOTIFICACAO_API_KEY"
          value = var.notificacao_api_key
        },
        {
          name  = "NOTIFICACAO_URL"
          value = var.notificacao_url
        },
        {
          name  = "APP_ENVIRONMENT"
          value = var.env
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_CLIENT_ID"
          value = var.keycloak_power_automate_client_id
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_SUB"
          value = var.keycloak_power_automate_sub
        },
        {
          name  = "DD_AGENT_HOST"
          value = "datadog.agilize.com.br"
        },
        {
          name  = "DD_TRACE_AGENT_PORT"
          value = "8126"
        },
        {
          name  = "DD_ENV"
          value = local.env
        },
        {
          name  = "DD_SERVICE"
          value = "api_accounting_new_worker_declaracoes_municipais"
        },
        {
          name  = "CONTABIL_URL"
          value = var.contabil_url
        },
        {
          name  = "CONTABIL_APIKEY"
          value = var.contabil_apikey
        }
      ]
    },
    {
      command = [
        "/worker",
        "-rod=show",
        "-command", "health_check_declaracoes_municipais",
        "-threads", "1",
        "-maxTasks", "1",
      ]
      cpu                                = local.worker_cpu
      cpu_max_percent                    = 80
      cpu_min_percent                    = 40
      deployment_maximum_percent         = 200
      deployment_minimum_healthy_percent = 100
      max_instances                      = 2
      memory                             = local.worker_memory
      memory_max_percent                 = 80
      memory_min_percent                 = 40
      min_instances                      = 2
      name                               = "worker-declaracoes-municipais-health-check"

      environments = [
        {
          name  = "LOG_LEVEL"
          value = local.log_level
        },
        {
          name  = "LOG_OUTPUT"
          value = "stdout"
        },
        {
          name  = "LOG_FORMAT"
          value = local.log_format
        },
        {
          name  = "DEBUG"
          value = local.env != "prd" ? "true" : "false"
        },
        {
          name  = "HTTP_ADDRESS"
          value = "0.0.0.0"
        },
        {
          name  = "HTTP_PORT"
          value = "80"
        },
        {
          name  = "HTTP_ALLOW_ORIGINS"
          value = "*"
        },
        {
          name  = "POSTGRES_URL"
          value = local.postgres_url
        },
        {
          name  = "POSTGRES_MAX_OPEN_CONN"
          value = local.postgres_max_open_conn
        },
        {
          name  = "POSTGRES_MAX_IDLE_CONN"
          value = local.postgres_max_idle_conn
        },
        {
          name  = "POSTGRES_MAX_CONN_LIFETIME"
          value = local.postgres_max_conn_lifetime
        },
        {
          name  = "CAMUNDA_BASE_URL"
          value = var.camunda_hostname
        },
        {
          name  = "CAMUNDA_AUTH_TOKEN"
          value = var.camunda_auth_token
        },
        {
          name  = "ACCOUNTING_ACESSOR_URL"
          value = var.acessor_hostname
        },
        {
          name  = "AWS_ACCESS_KEY_ID"
          value = var.aws_access_key_id_bucket
        },
        {
          name  = "AWS_BUCKET"
          value = "${local.env}-core-accounting-bucket"
        },
        {
          name  = "AWS_SECRET_ACCESS_KEY"
          value = var.aws_secret_access_key_bucket
        },
        {
          name  = "AWS_DEFAULT_REGION"
          value = "us-east-1"
        },
        {
          name  = "CERTIFICATE_PRIVATE_KEY"
          value = var.certificate_private_key
        },
        {
          name  = "TWO_CAPTCHA_API_KEY"
          value = var.two_captch_api_key
        },
        {
          name  = "CAP_MONSTER_API_KEY"
          value = var.cap_monster_api_key
        },
        {
          name  = "TEMPORAL_IO_ADDRESS"
          value = var.temporal_io_address
        },
        {
          name  = "NOTIFICACAO_API_KEY"
          value = var.notificacao_api_key
        },
        {
          name  = "NOTIFICACAO_URL"
          value = var.notificacao_url
        },
        {
          name  = "APP_ENVIRONMENT"
          value = var.env
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_CLIENT_ID"
          value = var.keycloak_power_automate_client_id
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_SUB"
          value = var.keycloak_power_automate_sub
        },
        {
          name  = "DD_AGENT_HOST"
          value = "datadog.agilize.com.br"
        },
        {
          name  = "DD_TRACE_AGENT_PORT"
          value = "8126"
        },
        {
          name  = "DD_ENV"
          value = local.env
        },
        {
          name  = "DD_SERVICE"
          value = "api_accounting_new_worker_declaracoes_municipais_health_check"
        },
        {
          name  = "CONTABIL_URL"
          value = var.contabil_url
        },
        {
          name  = "CONTABIL_APIKEY"
          value = var.contabil_apikey
        }
      ]
    },
    {
      command = [
        "/worker",
        "-command", "start_temporal_worker",
        "-threads", "1",
        "-maxTasks", "1",
      ]
      cpu                                = local.worker_cpu
      cpu_max_percent                    = 80
      cpu_min_percent                    = 40
      deployment_maximum_percent         = 200
      deployment_minimum_healthy_percent = 100
      max_instances                      = 2
      memory                             = local.worker_memory
      memory_max_percent                 = 80
      memory_min_percent                 = 40
      min_instances                      = 1
      name                               = "worker-temporal-io"

      environments = [
        {
          name  = "LOG_LEVEL"
          value = local.log_level
        },
        {
          name  = "LOG_OUTPUT"
          value = "stdout"
        },
        {
          name  = "LOG_FORMAT"
          value = local.log_format
        },
        {
          name  = "DEBUG"
          value = local.env != "prd" ? "true" : "false"
        },
        {
          name  = "HTTP_ADDRESS"
          value = "0.0.0.0"
        },
        {
          name  = "HTTP_PORT"
          value = "80"
        },
        {
          name  = "HTTP_ALLOW_ORIGINS"
          value = "*"
        },
        {
          name  = "POSTGRES_URL"
          value = local.postgres_url
        },
        {
          name  = "POSTGRES_MAX_OPEN_CONN"
          value = local.postgres_max_open_conn
        },
        {
          name  = "POSTGRES_MAX_IDLE_CONN"
          value = local.postgres_max_idle_conn
        },
        {
          name  = "POSTGRES_MAX_CONN_LIFETIME"
          value = local.postgres_max_conn_lifetime
        },
        {
          name  = "CAMUNDA_BASE_URL"
          value = var.camunda_hostname
        },
        {
          name  = "CAMUNDA_AUTH_TOKEN"
          value = var.camunda_auth_token
        },
        {
          name  = "ACCOUNTING_ACESSOR_URL"
          value = var.acessor_hostname
        },
        {
          name  = "AWS_ACCESS_KEY_ID"
          value = var.aws_access_key_id_bucket
        },
        {
          name  = "AWS_BUCKET"
          value = "${local.env}-core-accounting-bucket"
        },
        {
          name  = "AWS_SECRET_ACCESS_KEY"
          value = var.aws_secret_access_key_bucket
        },
        {
          name  = "AWS_DEFAULT_REGION"
          value = "us-east-1"
        },
        {
          name  = "CERTIFICATE_PRIVATE_KEY"
          value = var.certificate_private_key
        },
        {
          name  = "TWO_CAPTCHA_API_KEY"
          value = var.two_captch_api_key
        },
        {
          name  = "CAP_MONSTER_API_KEY"
          value = var.cap_monster_api_key
        },
        {
          name  = "TEMPORAL_IO_ADDRESS"
          value = var.temporal_io_address
        },
        {
          name  = "NOTIFICACAO_API_KEY"
          value = var.notificacao_api_key
        },
        {
          name  = "NOTIFICACAO_URL"
          value = var.notificacao_url
        },
        {
          name  = "APP_ENVIRONMENT"
          value = var.env
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_CLIENT_ID"
          value = var.keycloak_power_automate_client_id
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_SUB"
          value = var.keycloak_power_automate_sub
        },
        {
          name  = "DD_AGENT_HOST"
          value = "datadog.agilize.com.br"
        },
        {
          name  = "DD_TRACE_AGENT_PORT"
          value = "8126"
        },
        {
          name  = "DD_ENV"
          value = local.env
        },
        {
          name  = "DD_SERVICE"
          value = "api_accounting_new_worker_temporal_io"
        },
        {
          name  = "CONTABIL_URL"
          value = var.contabil_url
        },
        {
          name  = "CONTABIL_APIKEY"
          value = var.contabil_apikey
        }
      ]
    },
    {
      command = [
        "/worker",
        "-command", "iniciar_declaracao_mit",
        "-threads", "1",
        "-maxTasks", "1",
      ]
      cpu                                = local.worker_cpu
      cpu_max_percent                    = 80
      cpu_min_percent                    = 40
      deployment_maximum_percent         = 200
      deployment_minimum_healthy_percent = 100
      max_instances                      = 2
      memory                             = local.worker_memory
      memory_max_percent                 = 80
      memory_min_percent                 = 40
      min_instances                      = 2
      name                               = "worker-iniciar-declaracao-mit"

      environments = [
        {
          name  = "LOG_LEVEL"
          value = local.log_level
        },
        {
          name  = "LOG_OUTPUT"
          value = "stdout"
        },
        {
          name  = "LOG_FORMAT"
          value = local.log_format
        },
        {
          name  = "DEBUG"
          value = local.env != "prd" ? "true" : "false"
        },
        {
          name  = "HTTP_ADDRESS"
          value = "0.0.0.0"
        },
        {
          name  = "HTTP_PORT"
          value = "80"
        },
        {
          name  = "HTTP_ALLOW_ORIGINS"
          value = "*"
        },
        {
          name  = "POSTGRES_URL"
          value = local.postgres_url
        },
        {
          name  = "POSTGRES_MAX_OPEN_CONN"
          value = local.postgres_max_open_conn
        },
        {
          name  = "POSTGRES_MAX_IDLE_CONN"
          value = local.postgres_max_idle_conn
        },
        {
          name  = "POSTGRES_MAX_CONN_LIFETIME"
          value = local.postgres_max_conn_lifetime
        },
        {
          name  = "CAMUNDA_BASE_URL"
          value = var.camunda_hostname
        },
        {
          name  = "CAMUNDA_AUTH_TOKEN"
          value = var.camunda_auth_token
        },
        {
          name  = "ACCOUNTING_ACESSOR_URL"
          value = var.acessor_hostname
        },
        {
          name  = "AWS_ACCESS_KEY_ID"
          value = var.aws_access_key_id_bucket
        },
        {
          name  = "AWS_BUCKET"
          value = "${local.env}-core-accounting-bucket"
        },
        {
          name  = "AWS_SECRET_ACCESS_KEY"
          value = var.aws_secret_access_key_bucket
        },
        {
          name  = "AWS_DEFAULT_REGION"
          value = "us-east-1"
        },
        {
          name  = "CERTIFICATE_PRIVATE_KEY"
          value = var.certificate_private_key
        },
        {
          name  = "TWO_CAPTCHA_API_KEY"
          value = var.two_captch_api_key
        },
        {
          name  = "CAP_MONSTER_API_KEY"
          value = var.cap_monster_api_key
        },
        {
          name  = "TEMPORAL_IO_ADDRESS"
          value = var.temporal_io_address
        },
        {
          name  = "NOTIFICACAO_API_KEY"
          value = var.notificacao_api_key
        },
        {
          name  = "NOTIFICACAO_URL"
          value = var.notificacao_url
        },
        {
          name  = "APP_ENVIRONMENT"
          value = var.env
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_CLIENT_ID"
          value = var.keycloak_power_automate_client_id
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_SUB"
          value = var.keycloak_power_automate_sub
        },
        {
          name  = "DD_AGENT_HOST"
          value = "datadog.agilize.com.br"
        },
        {
          name  = "DD_TRACE_AGENT_PORT"
          value = "8126"
        },
        {
          name  = "DD_ENV"
          value = local.env
        },
        {
          name  = "DD_SERVICE"
          value = "api_accounting_new_worker_iniciar_declaracao_mit"
        },
        {
          name  = "CONTABIL_URL"
          value = var.contabil_url
        },
        {
          name  = "CONTABIL_APIKEY"
          value = var.contabil_apikey
        }
      ]
    },
    {
      command = [
        "/worker",
        "-command", "processar_declaracoes_mit",
        "-threads", "1",
        "-maxTasks", "1",
      ]
      cpu                                = local.worker_cpu
      cpu_max_percent                    = 80
      cpu_min_percent                    = 40
      deployment_maximum_percent         = 200
      deployment_minimum_healthy_percent = 100
      max_instances                      = 2
      memory                             = local.worker_memory
      memory_max_percent                 = 80
      memory_min_percent                 = 40
      min_instances                      = 2
      name                               = "worker-processar-declaracoes-mit"

      environments = [
        {
          name  = "LOG_LEVEL"
          value = local.log_level
        },
        {
          name  = "LOG_OUTPUT"
          value = "stdout"
        },
        {
          name  = "LOG_FORMAT"
          value = local.log_format
        },
        {
          name  = "DEBUG"
          value = local.env != "prd" ? "true" : "false"
        },
        {
          name  = "HTTP_ADDRESS"
          value = "0.0.0.0"
        },
        {
          name  = "HTTP_PORT"
          value = "80"
        },
        {
          name  = "HTTP_ALLOW_ORIGINS"
          value = "*"
        },
        {
          name  = "POSTGRES_URL"
          value = local.postgres_url
        },
        {
          name  = "POSTGRES_MAX_OPEN_CONN"
          value = local.postgres_max_open_conn
        },
        {
          name  = "POSTGRES_MAX_IDLE_CONN"
          value = local.postgres_max_idle_conn
        },
        {
          name  = "POSTGRES_MAX_CONN_LIFETIME"
          value = local.postgres_max_conn_lifetime
        },
        {
          name  = "CAMUNDA_BASE_URL"
          value = var.camunda_hostname
        },
        {
          name  = "CAMUNDA_AUTH_TOKEN"
          value = var.camunda_auth_token
        },
        {
          name  = "ACCOUNTING_ACESSOR_URL"
          value = var.acessor_hostname
        },
        {
          name  = "AWS_ACCESS_KEY_ID"
          value = var.aws_access_key_id_bucket
        },
        {
          name  = "AWS_BUCKET"
          value = "${local.env}-core-accounting-bucket"
        },
        {
          name  = "AWS_SECRET_ACCESS_KEY"
          value = var.aws_secret_access_key_bucket
        },
        {
          name  = "AWS_DEFAULT_REGION"
          value = "us-east-1"
        },
        {
          name  = "CERTIFICATE_PRIVATE_KEY"
          value = var.certificate_private_key
        },
        {
          name  = "TWO_CAPTCHA_API_KEY"
          value = var.two_captch_api_key
        },
        {
          name  = "CAP_MONSTER_API_KEY"
          value = var.cap_monster_api_key
        },
        {
          name  = "TEMPORAL_IO_ADDRESS"
          value = var.temporal_io_address
        },
        {
          name  = "NOTIFICACAO_API_KEY"
          value = var.notificacao_api_key
        },
        {
          name  = "NOTIFICACAO_URL"
          value = var.notificacao_url
        },
        {
          name  = "APP_ENVIRONMENT"
          value = var.env
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_CLIENT_ID"
          value = var.keycloak_power_automate_client_id
        },
        {
          name  = "KEYCLOAK_POWER_AUTOMATE_SUB"
          value = var.keycloak_power_automate_sub
        },
        {
          name  = "DD_AGENT_HOST"
          value = "datadog.agilize.com.br"
        },
        {
          name  = "DD_TRACE_AGENT_PORT"
          value = "8126"
        },
        {
          name  = "DD_ENV"
          value = local.env
        },
        {
          name  = "DD_SERVICE"
          value = "api_accounting_new_worker_processar_declaracoes_mit"
        },
        {
          name  = "CONTABIL_URL"
          value = var.contabil_url
        },
        {
          name  = "CONTABIL_APIKEY"
          value = var.contabil_apikey
        }
      ]
    },
  ]

  tags = {
    CostCenter  = "Engineer"
    Environment = local.env
    ManagedBy   = "Terraform"
    ProjectName = local.project_name
    COSTCENTER  = "Operações"
    LEGADO      = "false"
    OWNER       = "Carlos Soares"
    SCOPE       = "CONTABIL"
    SOURCE      = "Golang"
    SQUAD       = "OPS"
    TRIBE       = "Operations"
  }
}
