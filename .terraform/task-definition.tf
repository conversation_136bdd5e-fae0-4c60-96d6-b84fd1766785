resource "aws_ecs_task_definition" "this" {
  for_each                 = { for idx, service in local.services : idx => service }
  family                   = "${local.prefix}-${local.name}-${each.value.name}"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = each.value.cpu
  memory                   = each.value.memory
  execution_role_arn       = aws_iam_role.this.arn
  container_definitions = jsonencode(
    [
      {
        command     = each.value.command
        cpu         = each.value.cpu - local.datadog_cpu
        environment = lookup(each.value, "environments", [])
        image       = "${local.image}:${local.image_tag}"
        name        = each.value.name
        memory      = each.value.memory - local.datadog_memory

        logConfiguration = {
          logDriver = "awsfirelens"
          options = {
            "Name"       = "datadog"
            "apikey"     = "********************************"
            "dd_service" = each.value.name
            "dd_source"  = "golang"
            "dd_tags"    = "env:${local.env}"
            "provider"   = "ecs"
          }
        }

        portMappings = can(each.value.port) ? [
          {
            containerPort = each.value.port
            hostPort      = each.value.port
          }
        ] : []

        tags = [
          for key, value in merge(local.tags, { Name = "${local.prefix}-${local.name}-${each.value.name}" }) : {
            key   = key
            value = value
          }
        ]
      },
      {
        cpu       = local.datadog_cpu
        essential = true
        image     = "694620323152.dkr.ecr.us-east-1.amazonaws.com/fluentbit:latest"
        memory    = local.datadog_memory
        name      = "log_router"

        firelensConfiguration = {
          type = "fluentbit"
          options = {
            "config-file-type"        = "file"
            "config-file-value"       = "/fluent-bit/configs-custom/parse-json.conf"
            "enable-ecs-log-metadata" = "true"
          }
        }

        tags = [
          for key, value in merge(local.tags, { Name = "${local.prefix}-${local.name}-${each.value.name}-datadog" }) : {
            key   = key
            value = value
          }
        ]
      }
    ]
  )

  tags = merge(local.tags, {
    Name = "${local.prefix}-${local.name}-${each.value.name}"
  })
}

data "aws_ecs_task_definition" "this" {
  for_each        = { for idx, service in local.services : idx => service }
  task_definition = aws_ecs_task_definition.this[each.key].family
}
